@extends('layouts.app')

@section('title', 'Indah Berkah Abadi - Sistem Inventori Terpadu')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-6 lg:px-8 py-5 flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white font-bold text-lg">IBA</span>
                </div>
                <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 tracking-tight">Indah Berkah Abadi</h1>
            </div>
            <div class="flex items-center mr-4 lg:mr-8">
                <a href="{{ route('login') }}" class="btn btn-primary px-8 py-3 text-base font-semibold shadow-md hover:shadow-lg transition-all duration-200">
                    <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                    </svg>
                    Masuk
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-12 sm:py-16 lg:py-24">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="inline-flex items-center px-4 py-2 rounded-full text-xs sm:text-sm bg-blue-100 text-blue-800 mb-6 sm:mb-8">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Sistem Manajemen Logistik & Inventori
            </div>
            <h2 class="text-3xl sm:text-4xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
                Sistem Inventori<br class="hidden sm:block">
                <span class="text-blue-600">Terpadu</span>
            </h2>
            <p class="text-base sm:text-lg lg:text-xl text-gray-600 mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed">
                Solusi manajemen inventori lengkap untuk gudang pusat dan <span class="font-semibold text-blue-600">{{ $stats['stores'] }}+</span> lokasi toko.
                Pantau stok, kelola distribusi, dan buat laporan komprehensif dengan mudah.
            </p>
            <div class="flex flex-col sm:flex-row justify-center items-center gap-4 sm:gap-6">
                <a href="{{ route('login') }}" class="w-full sm:w-auto btn btn-primary text-base sm:text-lg px-8 py-4 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200">
                    <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Dashboard Gudang
                </a>
                <a href="{{ route('login') }}" class="w-full sm:w-auto btn btn-outline text-base sm:text-lg px-8 py-4 hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200">
                    <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Dashboard Toko
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-12 sm:py-16 lg:py-20 bg-white">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16">
                <h3 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6">Solusi Inventori Lengkap</h3>
                <p class="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Kelola seluruh rantai pasokan dari satu platform dengan pelacakan dan pelaporan real-time.
                </p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                @foreach($features as $feature)
                <div class="card hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                    <div class="card-header">
                        <div class="feature-icon feature-icon-{{ $feature['icon'] }} mb-4 mx-auto sm:mx-0"></div>
                        <h4 class="card-title text-center sm:text-left">{{ $feature['title'] }}</h4>
                        <p class="card-description text-center sm:text-left">{{ $feature['description'] }}</p>
                    </div>
                    <div class="card-content">
                        <ul class="space-y-2 text-sm text-gray-600">
                            @foreach($feature['items'] as $item)
                            <li class="flex items-start">
                                <svg class="mr-2 h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ $item }}
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-12 sm:py-16 lg:py-20 bg-gradient-to-r from-gray-50 to-blue-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 text-center">
                <div class="bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <div class="text-3xl sm:text-4xl lg:text-5xl font-bold text-blue-600 mb-2 sm:mb-3">{{ $stats['warehouse'] }}</div>
                    <div class="text-sm sm:text-base text-gray-600 font-medium">Gudang Pusat</div>
                </div>
                <div class="bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <div class="text-3xl sm:text-4xl lg:text-5xl font-bold text-green-600 mb-2 sm:mb-3">{{ $stats['stores'] }}+</div>
                    <div class="text-sm sm:text-base text-gray-600 font-medium">Lokasi Toko</div>
                </div>
                <div class="bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <div class="text-3xl sm:text-4xl lg:text-5xl font-bold text-orange-600 mb-2 sm:mb-3">{{ $stats['realtime_tracking'] }}%</div>
                    <div class="text-sm sm:text-base text-gray-600 font-medium">Pelacakan Real-time</div>
                </div>
                <div class="bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <div class="text-3xl sm:text-4xl lg:text-5xl font-bold text-purple-600 mb-2 sm:mb-3">{{ $stats['availability'] }}/7</div>
                    <div class="text-sm sm:text-base text-gray-600 font-medium">Ketersediaan Sistem</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 sm:py-16">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12">
                <div class="sm:col-span-2 lg:col-span-1">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="h-10 w-10 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl flex items-center justify-center">
                            <span class="text-white font-bold text-sm">IBA</span>
                        </div>
                        <span class="text-lg font-bold">Indah Berkah Abadi</span>
                    </div>
                    <p class="text-gray-400 text-sm sm:text-base leading-relaxed mb-6">
                        Solusi manajemen logistik dan inventori lengkap untuk bisnis modern dengan teknologi terdepan.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 sm:mb-6 text-base sm:text-lg">Platform</h4>
                    <ul class="space-y-2 sm:space-y-3 text-sm sm:text-base text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Dashboard Gudang</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Dashboard Toko</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Laporan</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Analitik</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 sm:mb-6 text-base sm:text-lg">Dukungan</h4>
                    <ul class="space-y-2 sm:space-y-3 text-sm sm:text-base text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Pusat Bantuan</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Dokumentasi</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Hubungi Kami</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Pelatihan</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4 sm:mb-6 text-base sm:text-lg">Perusahaan</h4>
                    <ul class="space-y-2 sm:space-y-3 text-sm sm:text-base text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Tentang Kami</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Karir</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Kebijakan Privasi</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Syarat Layanan</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 sm:mt-12 pt-8 text-center">
                <p class="text-gray-400 text-sm sm:text-base">&copy; 2025 Indah Berkah Abadi. Hak cipta dilindungi.</p>
            </div>
        </div>
    </footer>
</div>
@endsection
