/**
 * Independent JavaScript for User Dashboard Form Interactions
 * PT. Indah Berkah Abadi - Inventory System
 * Handles form interactions without conflicts with sidebar event listeners
 * 
 * Features:
 * - Proper event handling for all form elements
 * - Mobile-friendly form interactions
 * - Accessibility support (keyboard navigation, screen readers)
 * - Conflict prevention with sidebar navigation
 * - Form validation enhancements
 * - Auto-enhancement of existing form elements
 */

(function() {
    'use strict';

    // Debug logging
    const DEBUG = false;
    function log(message, data = null) {
        if (DEBUG) {
            console.log('[UserDashboardForms]', message, data || '');
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeForms);
    } else {
        initializeForms();
    }

    function initializeForms() {
        log('Initializing user dashboard forms...');

        // Initialize form inputs
        initializeFormInputs();
        
        // Initialize form buttons
        initializeFormButtons();
        
        // Initialize form containers
        initializeFormContainers();
        
        // Initialize accessibility features
        initializeAccessibility();
        
        // Prevent conflicts with sidebar
        preventSidebarConflicts();
        
        // Auto-enhance existing elements
        autoEnhanceExistingElements();

        log('User dashboard forms initialized successfully');
    }

    /**
     * Initialize form input elements
     */
    function initializeFormInputs() {
        const inputs = document.querySelectorAll(
            '.user-dashboard-form-input, .user-dashboard-form-input-enhanced, ' +
            '.user-dashboard-form-number, .user-dashboard-form-number-enhanced, ' +
            '.user-dashboard-form-password, .user-dashboard-form-password-enhanced, ' +
            '.user-dashboard-form-date, .user-dashboard-form-date-enhanced, ' +
            '.user-dashboard-form-textarea, .user-dashboard-form-textarea-enhanced'
        );
        
        inputs.forEach(input => {
            log('Initializing form input:', input.id || input.name || input.type);
            
            // Ensure proper z-index
            input.style.position = 'relative';
            input.style.zIndex = '10';
            
            // Add enhanced class based on input type
            enhanceInputElement(input);
            
            // Handle events
            input.addEventListener('focus', handleInputFocus);
            input.addEventListener('blur', handleInputBlur);
            input.addEventListener('input', handleInputChange);
            input.addEventListener('keydown', handleInputKeydown);
            
            // Prevent event bubbling that might interfere with sidebar (but not for sidebar elements)
            input.addEventListener('click', function(e) {
                // Don't interfere with sidebar elements
                if (e.target.closest('.sidebar, .sidebar-footer, .user-dashboard-user-menu')) {
                    log('Allowing sidebar input click to propagate');
                    return;
                }

                e.stopPropagation();
                log('Input clicked, preventing event bubbling');
            });
        });
        
        log(`Initialized ${inputs.length} form input elements`);
    }

    /**
     * Initialize form button elements
     */
    function initializeFormButtons() {
        const buttons = document.querySelectorAll(
            '.user-dashboard-form-submit, .user-dashboard-form-submit-enhanced, ' +
            '.user-dashboard-form-cancel, .user-dashboard-form-cancel-enhanced, ' +
            'button[type="submit"], input[type="submit"]'
        );
        
        buttons.forEach(button => {
            log('Initializing form button:', button.id || button.type || button.textContent);
            
            // Ensure proper z-index
            button.style.position = 'relative';
            button.style.zIndex = '10';
            
            // Add enhanced class based on button type
            enhanceButtonElement(button);
            
            // Handle events
            button.addEventListener('focus', handleButtonFocus);
            button.addEventListener('blur', handleButtonBlur);
            button.addEventListener('click', handleButtonClick);
            button.addEventListener('keydown', handleButtonKeydown);
        });
        
        log(`Initialized ${buttons.length} form button elements`);
    }

    /**
     * Initialize form container elements
     */
    function initializeFormContainers() {
        const containers = document.querySelectorAll(
            '.user-dashboard-form-container, .user-dashboard-form-container-enhanced, ' +
            '.user-dashboard-form-group, .user-dashboard-form-group-enhanced, ' +
            '.user-dashboard-filter-group, .user-dashboard-filter-group-enhanced'
        );
        
        containers.forEach(container => {
            log('Initializing form container:', container.className);
            
            // Ensure proper z-index
            container.style.position = 'relative';
            container.style.zIndex = container.classList.contains('user-dashboard-form-container') ? '5' : '10';
            
            // Add enhanced class
            enhanceContainerElement(container);
        });
        
        log(`Initialized ${containers.length} form container elements`);
    }

    /**
     * Initialize accessibility features
     */
    function initializeAccessibility() {
        // Add ARIA labels and roles for form elements
        const formElements = document.querySelectorAll(
            'input, textarea, select, button'
        );
        
        formElements.forEach(element => {
            // Ensure proper ARIA attributes
            if (!element.getAttribute('aria-label') && !element.getAttribute('aria-labelledby')) {
                const label = element.previousElementSibling || 
                             document.querySelector(`label[for="${element.id}"]`);
                if (label && label.tagName === 'LABEL') {
                    const labelId = label.id || `label-${element.id || Math.random().toString(36).substr(2, 9)}`;
                    label.id = labelId;
                    element.setAttribute('aria-labelledby', labelId);
                }
            }
            
            // Add role for form elements
            if (element.tagName === 'INPUT' && !element.getAttribute('role')) {
                element.setAttribute('role', 'textbox');
            }
            
            // Add aria-required for required fields
            if (element.hasAttribute('required') && !element.getAttribute('aria-required')) {
                element.setAttribute('aria-required', 'true');
            }
        });
    }

    /**
     * Prevent conflicts with sidebar event listeners
     */
    function preventSidebarConflicts() {
        // Find all form containers and ensure they don't interfere with sidebar
        const formContainers = document.querySelectorAll(
            'form, .user-dashboard-form-container-enhanced, ' +
            '.user-dashboard-form-group-enhanced, .user-dashboard-filter-group-enhanced'
        );

        formContainers.forEach(container => {
            // Skip if this container is inside the sidebar
            if (container.closest('.sidebar, .sidebar-footer, .user-dashboard-user-menu')) {
                log('Skipping sidebar container to prevent conflicts');
                return;
            }

            container.addEventListener('click', function(e) {
                // Don't interfere with sidebar elements
                if (e.target.closest('.sidebar, .sidebar-footer, .user-dashboard-user-menu, .user-dashboard-user-menu-button, .user-dashboard-user-menu-dropdown')) {
                    log('Allowing sidebar element click to propagate');
                    return;
                }

                // Only stop propagation if the click is on a form element
                if (e.target.matches('input, textarea, select, button, label')) {
                    e.stopPropagation();
                    log('Form container click prevented from reaching sidebar');
                }
            });
        });
    }

    /**
     * Auto-enhance existing form elements
     */
    function autoEnhanceExistingElements() {
        // Enhance existing inputs
        const existingInputs = document.querySelectorAll(
            'input:not([class*="enhanced"]), textarea:not([class*="enhanced"])'
        );
        existingInputs.forEach(enhanceInputElement);

        // Enhance existing buttons
        const existingButtons = document.querySelectorAll(
            'button:not([class*="enhanced"]), input[type="submit"]:not([class*="enhanced"])'
        );
        existingButtons.forEach(enhanceButtonElement);

        // Enhance existing containers
        const existingContainers = document.querySelectorAll(
            '.user-dashboard-form-group:not(.user-dashboard-form-group-enhanced), ' +
            '.user-dashboard-filter-group:not(.user-dashboard-filter-group-enhanced)'
        );
        existingContainers.forEach(enhanceContainerElement);
    }

    /**
     * Enhancement functions
     */
    function enhanceInputElement(input) {
        const type = input.type || input.tagName.toLowerCase();
        
        switch (type) {
            case 'text':
            case 'email':
            case 'search':
                if (!input.classList.contains('user-dashboard-form-input-enhanced')) {
                    input.classList.add('user-dashboard-form-input-enhanced');
                }
                break;
            case 'number':
                if (!input.classList.contains('user-dashboard-form-number-enhanced')) {
                    input.classList.add('user-dashboard-form-number-enhanced');
                }
                break;
            case 'password':
                if (!input.classList.contains('user-dashboard-form-password-enhanced')) {
                    input.classList.add('user-dashboard-form-password-enhanced');
                }
                break;
            case 'date':
                if (!input.classList.contains('user-dashboard-form-date-enhanced')) {
                    input.classList.add('user-dashboard-form-date-enhanced');
                }
                break;
            case 'textarea':
                if (!input.classList.contains('user-dashboard-form-textarea-enhanced')) {
                    input.classList.add('user-dashboard-form-textarea-enhanced');
                }
                break;
        }
    }

    function enhanceButtonElement(button) {
        const type = button.type || 'button';
        const isSubmit = type === 'submit' || button.classList.contains('btn-primary');
        
        if (isSubmit) {
            if (!button.classList.contains('user-dashboard-form-submit-enhanced')) {
                button.classList.add('user-dashboard-form-submit-enhanced');
            }
        } else {
            if (!button.classList.contains('user-dashboard-form-cancel-enhanced')) {
                button.classList.add('user-dashboard-form-cancel-enhanced');
            }
        }
    }

    function enhanceContainerElement(container) {
        if (container.classList.contains('user-dashboard-form-container')) {
            container.classList.add('user-dashboard-form-container-enhanced');
        } else if (container.classList.contains('user-dashboard-form-group')) {
            container.classList.add('user-dashboard-form-group-enhanced');
        } else if (container.classList.contains('user-dashboard-filter-group')) {
            container.classList.add('user-dashboard-filter-group-enhanced');
        }
    }

    /**
     * Event handlers
     */
    function handleInputFocus(e) {
        log('Input focused:', e.target.id || e.target.name);
        e.target.style.zIndex = '50'; // Increase z-index when focused
        
        // Add focus class for additional styling
        e.target.classList.add('user-dashboard-form-focused');
    }

    function handleInputBlur(e) {
        log('Input blurred:', e.target.id || e.target.name);
        e.target.style.zIndex = '10'; // Reset z-index when blurred
        
        // Remove focus class
        e.target.classList.remove('user-dashboard-form-focused');
    }

    function handleInputChange(e) {
        log('Input changed:', e.target.id || e.target.name, 'Value:', e.target.value);
        
        // Trigger custom event for other scripts to listen to
        const customEvent = new CustomEvent('userDashboardFormChange', {
            detail: {
                element: e.target,
                value: e.target.value,
                name: e.target.name || e.target.id
            }
        });
        document.dispatchEvent(customEvent);
    }

    function handleInputKeydown(e) {
        // Handle keyboard navigation
        if (e.key === 'Escape') {
            e.target.blur();
            log('Escape pressed, blurring input');
        }
        
        // Prevent arrow keys from interfering with sidebar navigation
        if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
            e.stopPropagation();
        }
    }

    function handleButtonFocus(e) {
        log('Button focused:', e.target.textContent || e.target.value);
        e.target.style.zIndex = '50';
    }

    function handleButtonBlur(e) {
        log('Button blurred:', e.target.textContent || e.target.value);
        e.target.style.zIndex = '10';
    }

    function handleButtonClick(e) {
        log('Button clicked:', e.target.textContent || e.target.value);

        // Don't interfere with sidebar elements
        if (e.target.closest('.sidebar, .sidebar-footer, .user-dashboard-user-menu')) {
            log('Allowing sidebar button click to propagate');
            return;
        }

        // Prevent event bubbling to sidebar
        e.stopPropagation();

        // Add click animation
        e.target.style.transform = 'scale(0.98)';
        setTimeout(() => {
            e.target.style.transform = '';
        }, 150);
    }

    function handleButtonKeydown(e) {
        // Handle keyboard activation
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            e.target.click();
        }
        
        if (e.key === 'Escape') {
            e.target.blur();
        }
    }

    /**
     * Public API for external scripts
     */
    window.UserDashboardForms = {
        reinitialize: initializeForms,
        enhanceElement: function(element) {
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                enhanceInputElement(element);
            } else if (element.tagName === 'BUTTON') {
                enhanceButtonElement(element);
            }
        },
        enableDebug: function() {
            DEBUG = true;
            log('Debug mode enabled');
        },
        disableDebug: function() {
            DEBUG = false;
        }
    };

    log('User dashboard forms script loaded successfully');
})();
