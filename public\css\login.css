/* Login Page Specific Styles - Independent CSS to prevent conflicts */
.iba-login-container {
    min-height: 100vh;
    background: #2563eb;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.iba-login-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 24rem;
    overflow: hidden;
}

.iba-login-header {
    padding: 2.5rem 2rem 1.5rem;
    text-align: center;
}

.iba-login-back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #2563eb;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 2rem;
    transition: color 0.3s ease;
}

.iba-login-back-link:hover {
    color: #1d4ed8;
}

.iba-login-title {
    font-size: 1.875rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.iba-login-subtitle {
    color: #64748b;
    font-size: 0.875rem;
}

.iba-login-form {
    padding: 0 2rem 3rem;
}

.iba-login-form-group {
    margin-bottom: 1.5rem;
}

.iba-login-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.iba-login-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
}

.iba-login-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.iba-login-input-error {
    border-color: #dc2626;
}

.iba-login-input-error:focus {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.iba-login-error {
    color: #dc2626;
    font-size: 0.75rem;
    margin-top: 0.5rem;
}

.iba-login-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
}

.iba-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5);
}

.iba-login-btn:active {
    transform: translateY(0);
}

.iba-login-alert {
    padding: 1rem;
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.iba-login-alert-success {
    background: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.2);
    color: #059669;
}

.iba-login-alert-info {
    background: rgba(37, 99, 235, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.2);
    color: #2563eb;
}

.iba-login-alert-error {
    background: rgba(220, 38, 38, 0.1);
    border: 1px solid rgba(220, 38, 38, 0.2);
    color: #dc2626;
}

/* Loading state */
.iba-login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.iba-login-btn:disabled:hover {
    transform: none;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .iba-login-container {
        padding: 0.5rem;
    }
    
    .iba-login-card {
        max-width: 100%;
        margin: 0;
    }
    
    .iba-login-header {
        padding: 2rem 1.5rem 1.5rem;
    }
    
    .iba-login-form {
        padding: 0 1.5rem 2rem;
    }
    
    .iba-login-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .iba-login-title {
        font-size: 1.375rem;
    }
}
