/**
 * Independent JavaScript for User Dashboard Product Management
 * PT. Indah Berkah Abadi - Inventory System
 * Prevents conflicts with other JavaScript frameworks and ensures proper functionality
 * 
 * Features:
 * - Product stock adjustment form handling
 * - Real-time preview updates
 * - Mobile-friendly interactions
 * - Validation and error handling
 * - Z-index management for proper layering
 */

(function() {
    'use strict';

    // Namespace to prevent conflicts
    const UserProductManagement = {
        // Configuration
        config: {
            selectors: {
                adjustmentForm: '.user-dashboard-product-form',
                adjustmentType: '#adjustmentType',
                quantityInput: '#quantityInput',
                quantityGroup: '#quantityGroup',
                preview: '#adjustmentPreview',
                validationWarning: '#validationWarning',
                submitButton: '#submitButton',
                quickForms: '.user-dashboard-product-quick-form'
            },
            zIndex: {
                base: 10,
                interactive: 25,
                modal: 50
            }
        },

        // Initialize the module
        init: function() {
            this.bindEvents();
            this.initializeFormState();
            this.setupMobileOptimizations();
        },

        // Bind event listeners
        bindEvents: function() {
            const self = this;

            // Form submission handling
            const forms = document.querySelectorAll(this.config.selectors.quickForms);
            forms.forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    self.handleQuickFormSubmit(e, this);
                });
            });

            // Product adjustment form handling
            const adjustmentForm = document.getElementById('productAdjustmentForm');
            if (adjustmentForm) {
                adjustmentForm.addEventListener('submit', function(e) {
                    self.handleAdjustmentFormSubmit(e, this);
                });
            }

            // Real-time validation for adjustment form
            this.setupRealTimeValidation();

            // Mobile touch optimizations
            this.setupTouchOptimizations();

            // Window resize handling
            window.addEventListener('resize', function() {
                self.handleResize();
            });

            // Focus management for accessibility
            this.setupFocusManagement();
        },

        // Initialize form state
        initializeFormState: function() {
            const adjustmentType = document.querySelector(this.config.selectors.adjustmentType);
            if (adjustmentType) {
                // Trigger initial state update if there's a selected value
                if (adjustmentType.value) {
                    this.updateQuantityField();
                }
            }
        },

        // Handle quick form submissions with confirmation
        handleQuickFormSubmit: function(event, form) {
            // Check if this is a set-zero form (handled by custom modal)
            if (form.classList.contains('user-dashboard-product-quick-form')) {
                // Let the custom modal handler take care of this
                event.preventDefault();
                return false;
            }

            // For other forms, use default behavior
            const productName = this.extractProductName(form);
            const confirmMessage = `Yakin ingin melakukan tindakan ini pada ${productName}?`;

            if (!confirm(confirmMessage)) {
                event.preventDefault();
                return false;
            }

            // Add loading state
            this.setFormLoading(form, true);

            // Allow form to submit normally
            return true;
        },

        // Handle product adjustment form submission
        handleAdjustmentFormSubmit: function(event, form) {
            // Check if this form has custom modal handling
            if (form.id === 'productAdjustmentForm') {
                // Let the custom modal handler in the view take care of this
                // The form submission is handled by the modal confirmation
                return true;
            }

            const self = this;

            // Perform client-side validation for other forms
            const validationResult = this.validateAdjustmentForm(form);

            if (!validationResult.isValid) {
                event.preventDefault();
                this.showValidationErrors(validationResult.errors);
                return false;
            }

            // Add loading state
            this.setFormLoading(form, true);

            // Allow form to submit
            return true;
        },

        // Setup real-time validation for adjustment form
        setupRealTimeValidation: function() {
            const adjustmentTypeSelect = document.getElementById('adjustmentType');
            const quantityInput = document.getElementById('quantityInput');
            const notesInput = document.querySelector('textarea[name="notes"]');

            if (adjustmentTypeSelect) {
                adjustmentTypeSelect.addEventListener('change', this.validateFormField.bind(this));
            }

            if (quantityInput) {
                quantityInput.addEventListener('input', this.validateFormField.bind(this));
                quantityInput.addEventListener('blur', this.validateFormField.bind(this));
            }

            if (notesInput) {
                notesInput.addEventListener('input', this.validateFormField.bind(this));
            }
        },

        // Validate individual form field
        validateFormField: function(event) {
            const field = event.target;
            const fieldName = field.name;
            const fieldValue = field.value;

            // Clear previous field-specific errors
            this.clearFieldError(field);

            // Validate based on field type
            switch (fieldName) {
                case 'adjustment_type':
                    if (!fieldValue) {
                        this.showFieldError(field, 'Silakan pilih jenis penyesuaian stok');
                    }
                    break;

                case 'quantity':
                    const adjustmentType = document.getElementById('adjustmentType').value;
                    if (adjustmentType === 'subtract') {
                        if (!fieldValue || fieldValue <= 0) {
                            this.showFieldError(field, 'Jumlah pengurangan harus lebih dari 0');
                        } else if (!Number.isInteger(Number(fieldValue))) {
                            this.showFieldError(field, 'Jumlah harus berupa angka bulat');
                        } else {
                            // Check against current stock (if available)
                            const currentStock = this.getCurrentStockValue();
                            if (currentStock && Number(fieldValue) > currentStock) {
                                this.showFieldError(field, `Jumlah pengurangan (${fieldValue}) melebihi stok tersedia (${currentStock})`);
                            }
                        }
                    }
                    break;

                case 'notes':
                    if (fieldValue && fieldValue.length > 500) {
                        this.showFieldError(field, 'Catatan terlalu panjang. Maksimal 500 karakter');
                    }
                    break;
            }
        },

        // Extract product name from form context
        extractProductName: function(form) {
            const row = form.closest('.user-dashboard-product-table-row');
            if (row) {
                const nameElement = row.querySelector('.user-dashboard-product-name');
                if (nameElement) {
                    return nameElement.textContent.trim();
                }
            }
            return 'produk ini';
        },

        // Set form loading state
        setFormLoading: function(form, isLoading) {
            const button = form.querySelector('button[type="submit"]');
            if (button) {
                if (isLoading) {
                    button.disabled = true;
                    button.style.opacity = '0.6';
                    button.style.cursor = 'not-allowed';
                    
                    // Add loading text
                    const originalText = button.innerHTML;
                    button.setAttribute('data-original-text', originalText);
                    button.innerHTML = `
                        <svg class="user-dashboard-product-btn-icon animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Memproses...
                    `;
                } else {
                    button.disabled = false;
                    button.style.opacity = '';
                    button.style.cursor = '';
                    
                    // Restore original text
                    const originalText = button.getAttribute('data-original-text');
                    if (originalText) {
                        button.innerHTML = originalText;
                        button.removeAttribute('data-original-text');
                    }
                }
            }
        },

        // Setup touch optimizations for mobile devices
        setupTouchOptimizations: function() {
            const buttons = document.querySelectorAll('.user-dashboard-product-btn');
            
            buttons.forEach(function(button) {
                // Ensure minimum touch target size
                const rect = button.getBoundingClientRect();
                if (rect.height < 44) {
                    button.style.minHeight = '44px';
                }
                if (rect.width < 44) {
                    button.style.minWidth = '44px';
                }

                // Add touch feedback
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                }, { passive: true });

                button.addEventListener('touchend', function() {
                    this.style.transform = '';
                }, { passive: true });

                button.addEventListener('touchcancel', function() {
                    this.style.transform = '';
                }, { passive: true });
            });
        },

        // Setup focus management for accessibility
        setupFocusManagement: function() {
            const interactiveElements = document.querySelectorAll(
                '.user-dashboard-product-btn, .user-dashboard-form-input-enhanced, .user-dashboard-form-submit-enhanced'
            );

            interactiveElements.forEach(function(element) {
                element.addEventListener('focus', function() {
                    this.style.zIndex = '50';
                });

                element.addEventListener('blur', function() {
                    this.style.zIndex = '';
                });
            });
        },

        // Handle window resize events
        handleResize: function() {
            // Recalculate table responsiveness
            this.adjustTableResponsiveness();
            
            // Adjust button layouts on mobile
            this.adjustMobileButtonLayouts();
        },

        // Adjust table responsiveness
        adjustTableResponsiveness: function() {
            const tableContainer = document.querySelector('.user-dashboard-product-table-container');
            const table = document.querySelector('.user-dashboard-product-table');
            
            if (tableContainer && table) {
                const containerWidth = tableContainer.offsetWidth;
                const tableWidth = table.scrollWidth;
                
                if (tableWidth > containerWidth) {
                    tableContainer.style.overflowX = 'auto';
                    // Add scroll indicator
                    tableContainer.classList.add('scrollable');
                } else {
                    tableContainer.style.overflowX = 'visible';
                    tableContainer.classList.remove('scrollable');
                }
            }
        },

        // Adjust button layouts for mobile
        adjustMobileButtonLayouts: function() {
            const isMobile = window.innerWidth <= 768;
            const actionContainers = document.querySelectorAll('.user-dashboard-product-actions');
            
            actionContainers.forEach(function(container) {
                if (isMobile) {
                    container.style.flexDirection = 'column';
                    container.style.alignItems = 'stretch';
                    container.style.gap = '0.25rem';
                    
                    // Make buttons full width on mobile
                    const buttons = container.querySelectorAll('.user-dashboard-product-btn');
                    buttons.forEach(function(button) {
                        button.style.width = '100%';
                        button.style.justifyContent = 'center';
                    });
                } else {
                    container.style.flexDirection = '';
                    container.style.alignItems = '';
                    container.style.gap = '';
                    
                    // Reset button widths on desktop
                    const buttons = container.querySelectorAll('.user-dashboard-product-btn');
                    buttons.forEach(function(button) {
                        button.style.width = '';
                        button.style.justifyContent = '';
                    });
                }
            });
        },

        // Setup mobile-specific optimizations
        setupMobileOptimizations: function() {
            // Only run on mobile devices
            if (window.innerWidth <= 768) {
                this.optimizeForMobile();
            }
        },

        // Optimize interface for mobile devices
        optimizeForMobile: function() {
            // Improve scroll performance
            const scrollableElements = document.querySelectorAll('.user-dashboard-product-table-container');
            scrollableElements.forEach(function(element) {
                element.style.webkitOverflowScrolling = 'touch';
                element.style.scrollBehavior = 'smooth';
            });

            // Add mobile-specific classes
            document.body.classList.add('user-dashboard-mobile-optimized');

            // Optimize form inputs for mobile
            const inputs = document.querySelectorAll('.user-dashboard-form-input-enhanced, .user-dashboard-form-number-enhanced');
            inputs.forEach(function(input) {
                // Prevent zoom on focus for iOS
                if (input.type === 'number') {
                    input.setAttribute('inputmode', 'numeric');
                }
                
                // Add mobile-friendly attributes
                input.style.fontSize = '16px'; // Prevent iOS zoom
            });
        },

        // Utility function to show notifications
        showNotification: function(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `user-dashboard-notification user-dashboard-notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#fee2e2' : '#d1fae5'};
                color: ${type === 'error' ? '#991b1b' : '#065f46'};
                padding: 1rem;
                border-radius: 0.5rem;
                border: 1px solid ${type === 'error' ? '#fecaca' : '#a7f3d0'};
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                max-width: 300px;
                font-size: 0.875rem;
                line-height: 1.4;
            `;
            notification.textContent = message;

            // Add to page
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(function() {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);

            // Allow manual close
            notification.addEventListener('click', function() {
                if (this.parentNode) {
                    this.parentNode.removeChild(this);
                }
            });
        },

        // Validate entire adjustment form
        validateAdjustmentForm: function(form) {
            const errors = [];
            const adjustmentType = form.querySelector('select[name="adjustment_type"]').value;
            const quantity = form.querySelector('input[name="quantity"]').value;
            const notes = form.querySelector('textarea[name="notes"]').value;

            // Validate adjustment type
            if (!adjustmentType) {
                errors.push('Silakan pilih jenis penyesuaian stok (Kurangi atau Habis)');
            }

            // Validate quantity for subtract type
            if (adjustmentType === 'subtract') {
                if (!quantity || quantity <= 0) {
                    errors.push('Jumlah pengurangan wajib diisi dan harus lebih dari 0');
                } else if (!Number.isInteger(Number(quantity))) {
                    errors.push('Jumlah stok harus berupa angka bulat');
                } else {
                    const currentStock = this.getCurrentStockValue();
                    if (currentStock && Number(quantity) > currentStock) {
                        errors.push(`Jumlah pengurangan (${quantity}) melebihi stok tersedia (${currentStock})`);
                    }
                }
            }

            // Validate notes length
            if (notes && notes.length > 500) {
                errors.push('Catatan terlalu panjang. Maksimal 500 karakter diperbolehkan');
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        },

        // Show validation errors
        showValidationErrors: function(errors) {
            // Remove existing error display
            const existingError = document.querySelector('.user-dashboard-validation-errors');
            if (existingError) {
                existingError.remove();
            }

            // Create error container
            const errorContainer = document.createElement('div');
            errorContainer.className = 'user-dashboard-validation-errors user-dashboard-product-error-container';

            const errorHeader = document.createElement('div');
            errorHeader.className = 'user-dashboard-product-error-header';
            errorHeader.innerHTML = `
                <svg class="user-dashboard-product-error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h4 class="user-dashboard-product-error-title">Silakan Perbaiki Kesalahan Berikut</h4>
            `;

            const errorList = document.createElement('ul');
            errorList.className = 'user-dashboard-product-error-list';

            errors.forEach(function(error) {
                const errorItem = document.createElement('li');
                errorItem.className = 'user-dashboard-product-error-item';
                errorItem.textContent = error;
                errorList.appendChild(errorItem);
            });

            errorContainer.appendChild(errorHeader);
            errorContainer.appendChild(errorList);

            // Insert before form
            const form = document.getElementById('productAdjustmentForm');
            if (form) {
                form.parentNode.insertBefore(errorContainer, form);

                // Scroll to error
                errorContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        },

        // Show field-specific error
        showFieldError: function(field, message) {
            // Remove existing error for this field
            this.clearFieldError(field);

            // Create error element
            const errorElement = document.createElement('div');
            errorElement.className = 'user-dashboard-form-error-enhanced user-dashboard-field-error';
            errorElement.textContent = message;

            // Insert after field
            field.parentNode.appendChild(errorElement);

            // Add error styling to field
            field.classList.add('user-dashboard-form-input-error');
        },

        // Clear field-specific error
        clearFieldError: function(field) {
            // Remove error message
            const existingError = field.parentNode.querySelector('.user-dashboard-field-error');
            if (existingError) {
                existingError.remove();
            }

            // Remove error styling
            field.classList.remove('user-dashboard-form-input-error');
        },

        // Get current stock value from page
        getCurrentStockValue: function() {
            const stockElement = document.querySelector('.user-dashboard-product-stock-value');
            if (stockElement) {
                const stockText = stockElement.textContent.replace(/[^\d]/g, '');
                return parseInt(stockText) || 0;
            }
            return 0;
        },

        // Get product name from page
        getProductNameFromForm: function() {
            const titleElement = document.querySelector('.user-dashboard-header-subtitle');
            if (titleElement) {
                const titleText = titleElement.textContent;
                const productName = titleText.split(' - ')[0];
                return productName || 'produk ini';
            }
            return 'produk ini';
        },

        // Error handling
        handleError: function(error) {
            console.error('User Product Management Error:', error);
            this.showNotification('Terjadi kesalahan. Silakan coba lagi.', 'error');
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            UserProductManagement.init();
        });
    } else {
        UserProductManagement.init();
    }

    // Expose to global scope for external access if needed
    window.UserProductManagement = UserProductManagement;

})();

/**
 * User Dashboard Modal Manager
 * Handles custom modal dialogs for PT. Indah Berkah Abadi inventory system
 */
(function() {
    'use strict';

    const UserModalManager = {
        // Configuration
        config: {
            modalClass: 'user-dashboard-modal-overlay',
            showClass: 'user-dashboard-modal-show',
            enteringClass: 'user-dashboard-modal-entering',
            leavingClass: 'user-dashboard-modal-leaving',
            animationDuration: 300
        },

        // Current modal state
        currentModal: null,
        previousFocus: null,
        confirmCallback: null,

        // Initialize modal manager
        init: function() {
            this.bindGlobalEvents();
        },

        // Bind global event listeners
        bindGlobalEvents: function() {
            const self = this;

            // Handle escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && self.currentModal) {
                    self.closeModal(self.currentModal);
                }
            });

            // Handle click outside modal
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains(self.config.modalClass)) {
                    self.closeModal(self.currentModal);
                }
            });
        },

        // Show modal with configuration
        showModal: function(modalId, options = {}) {
            const modal = document.getElementById(modalId);
            if (!modal) {
                console.error('Modal not found:', modalId);
                return false;
            }

            // Store current focus
            this.previousFocus = document.activeElement;
            this.currentModal = modalId;
            this.confirmCallback = options.onConfirm || null;

            // Populate modal content
            this.populateModal(modalId, options);

            // Show modal with animation
            modal.style.display = 'flex';
            modal.classList.add(this.config.enteringClass);

            // Force reflow for animation
            modal.offsetHeight;

            modal.classList.add(this.config.showClass);
            modal.setAttribute('aria-hidden', 'false');

            // Focus management
            setTimeout(() => {
                modal.classList.remove(this.config.enteringClass);
                this.focusModal(modal);
            }, this.config.animationDuration);

            // Prevent body scroll
            document.body.style.overflow = 'hidden';

            return true;
        },

        // Close modal
        closeModal: function(modalId) {
            const modal = document.getElementById(modalId);
            if (!modal) return false;

            modal.classList.add(this.config.leavingClass);
            modal.classList.remove(this.config.showClass);

            setTimeout(() => {
                modal.style.display = 'none';
                modal.classList.remove(this.config.leavingClass);
                modal.setAttribute('aria-hidden', 'true');

                // Restore focus
                if (this.previousFocus) {
                    this.previousFocus.focus();
                    this.previousFocus = null;
                }

                // Restore body scroll
                document.body.style.overflow = '';

                // Clear state
                this.currentModal = null;
                this.confirmCallback = null;
            }, this.config.animationDuration);

            return true;
        },

        // Populate modal with content
        populateModal: function(modalId, options) {
            const modal = document.getElementById(modalId);

            // Set title
            if (options.title) {
                const titleElement = modal.querySelector(`#${modalId}-title`);
                if (titleElement) titleElement.textContent = options.title;
            }

            // Set message
            if (options.message) {
                const messageElement = modal.querySelector(`#${modalId}-message`);
                if (messageElement) messageElement.textContent = options.message;
            }

            // Set product info
            if (options.productInfo) {
                this.setProductInfo(modalId, options.productInfo);
            }

            // Set impact preview
            if (options.impactPreview) {
                this.setImpactPreview(modalId, options.impactPreview);
            }

            // Set button text and styles
            if (options.confirmText) {
                const confirmBtn = modal.querySelector(`#${modalId}-confirm-btn`);
                if (confirmBtn) {
                    confirmBtn.innerHTML = `
                        <svg class="user-dashboard-modal-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        ${options.confirmText}
                    `;
                }
            }

            if (options.cancelText) {
                const cancelBtn = modal.querySelector(`#${modalId}-cancel-btn`);
                if (cancelBtn) {
                    cancelBtn.innerHTML = `
                        <svg class="user-dashboard-modal-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        ${options.cancelText}
                    `;
                }
            }

            // Set button style for dangerous actions
            if (options.isDangerous) {
                const confirmBtn = modal.querySelector(`#${modalId}-confirm-btn`);
                if (confirmBtn) {
                    confirmBtn.classList.remove('user-dashboard-modal-btn-primary');
                    confirmBtn.classList.add('user-dashboard-modal-btn-danger');
                }
            } else {
                const confirmBtn = modal.querySelector(`#${modalId}-confirm-btn`);
                if (confirmBtn) {
                    confirmBtn.classList.remove('user-dashboard-modal-btn-danger');
                    confirmBtn.classList.add('user-dashboard-modal-btn-primary');
                }
            }
        },

        // Set product information
        setProductInfo: function(modalId, productInfo) {
            const productInfoSection = document.querySelector(`#${modalId}-product-info`);
            if (!productInfoSection) return;

            productInfoSection.style.display = 'block';

            const productName = document.querySelector(`#${modalId}-product-name`);
            const currentStock = document.querySelector(`#${modalId}-current-stock`);

            if (productName && productInfo.name) {
                productName.textContent = productInfo.name;
            }

            if (currentStock && productInfo.currentStock !== undefined) {
                currentStock.textContent = `${productInfo.currentStock.toLocaleString()} unit`;
            }
        },

        // Set impact preview
        setImpactPreview: function(modalId, impactPreview) {
            const impactSection = document.querySelector(`#${modalId}-impact-preview`);
            if (!impactSection) return;

            impactSection.style.display = 'block';

            const beforeStock = document.querySelector(`#${modalId}-before-stock`);
            const changeAmount = document.querySelector(`#${modalId}-change-amount`);
            const afterStock = document.querySelector(`#${modalId}-after-stock`);

            if (beforeStock && impactPreview.beforeStock !== undefined) {
                beforeStock.textContent = `${impactPreview.beforeStock.toLocaleString()} unit`;
            }

            if (changeAmount && impactPreview.changeAmount !== undefined) {
                const changeText = impactPreview.changeAmount > 0
                    ? `+${impactPreview.changeAmount.toLocaleString()}`
                    : impactPreview.changeAmount.toLocaleString();
                changeAmount.textContent = `${changeText} unit`;
            }

            if (afterStock && impactPreview.afterStock !== undefined) {
                afterStock.textContent = `${impactPreview.afterStock.toLocaleString()} unit`;
            }
        },

        // Handle confirm action
        confirmAction: function(modalId) {
            if (this.confirmCallback && typeof this.confirmCallback === 'function') {
                const result = this.confirmCallback();

                // Close modal if callback returns true or undefined
                if (result !== false) {
                    this.closeModal(modalId);
                }
            } else {
                // Default behavior: just close modal
                this.closeModal(modalId);
            }
        },

        // Focus management for accessibility
        focusModal: function(modal) {
            // Focus the first focusable element in the modal
            const focusableElements = modal.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );

            if (focusableElements.length > 0) {
                focusableElements[0].focus();
            } else {
                modal.focus();
            }
        },

        // Utility method to show stock reduction confirmation
        showStockReductionConfirmation: function(options) {
            return this.showModal('stock-reduction-modal', {
                title: 'Konfirmasi Pengurangan Stok',
                message: `Apakah Anda yakin ingin mengurangi stok produk "${options.productName}" sebanyak ${options.quantity} unit?`,
                productInfo: {
                    name: options.productName,
                    currentStock: options.currentStock
                },
                impactPreview: {
                    beforeStock: options.currentStock,
                    changeAmount: -options.quantity,
                    afterStock: options.currentStock - options.quantity
                },
                confirmText: 'Ya, Kurangi Stok',
                cancelText: 'Batal',
                isDangerous: false,
                onConfirm: options.onConfirm
            });
        },

        // Utility method to show set-to-zero confirmation
        showSetToZeroConfirmation: function(options) {
            return this.showModal('set-zero-modal', {
                title: 'Konfirmasi Habis Stok',
                message: `⚠️ PERINGATAN: Anda akan mengatur stok produk "${options.productName}" menjadi HABIS (0 unit).\n\nTindakan ini akan menghapus semua stok yang tersedia. Pastikan ini adalah tindakan yang benar.`,
                productInfo: {
                    name: options.productName,
                    currentStock: options.currentStock
                },
                impactPreview: {
                    beforeStock: options.currentStock,
                    changeAmount: -options.currentStock,
                    afterStock: 0
                },
                confirmText: 'Ya, Habiskan Stok',
                cancelText: 'Batal',
                isDangerous: true,
                onConfirm: options.onConfirm
            });
        },

        // Show generic confirmation dialog
        showConfirmation: function(options) {
            return this.showModal('generic-confirmation-modal', {
                title: options.title || 'Konfirmasi',
                message: options.message || 'Apakah Anda yakin?',
                confirmText: options.confirmText || 'Ya',
                cancelText: options.cancelText || 'Batal',
                isDangerous: options.isDangerous || false,
                onConfirm: options.onConfirm
            });
        }
    };

    // Initialize modal manager when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            UserModalManager.init();
        });
    } else {
        UserModalManager.init();
    }

    // Expose to global scope
    window.UserModalManager = UserModalManager;

})();
