@extends('layouts.app')

@section('title', 'Masuk - Indah Berkah Abadi')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/login.css') }}">
@endpush

@section('content')
<div class="iba-login-container">
    <div class="iba-login-card">
        <!-- Header -->
        <div class="iba-login-header">
            <a href="{{ route('home') }}" class="iba-login-back-link">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Ke<PERSON><PERSON> ke Beranda
            </a>

            <h2 class="iba-login-title"><PERSON><PERSON><PERSON></h2>
            <p class="iba-login-subtitle">Akses dashboard manajemen inventori Anda</p>
        </div>

        <!-- Form -->
        <div class="iba-login-form">
            @if(session('success'))
                <div class="iba-login-alert iba-login-alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('info'))
                <div class="iba-login-alert iba-login-alert-info">
                    {{ session('info') }}
                </div>
            @endif

            @if(session('error'))
                <div class="iba-login-alert iba-login-alert-error">
                    {{ session('error') }}
                </div>
            @endif

            <form method="POST" action="{{ route('login.post') }}">
                @csrf
                
                <div class="iba-login-form-group">
                    <label for="email" class="iba-login-label">Email</label>
                    <input 
                        id="email" 
                        name="email" 
                        type="email" 
                        class="iba-login-input @error('email') iba-login-input-error @enderror"
                        placeholder="Masukkan email Anda"
                        value="{{ old('email') }}"
                        required
                        autocomplete="email"
                        autofocus
                    >
                    @error('email')
                        <p class="iba-login-error">{{ $message }}</p>
                    @enderror
                </div>

                <div class="iba-login-form-group">
                    <label for="password" class="iba-login-label">Kata Sandi</label>
                    <input 
                        id="password" 
                        name="password" 
                        type="password" 
                        class="iba-login-input @error('password') iba-login-input-error @enderror"
                        placeholder="Masukkan kata sandi"
                        required
                        autocomplete="current-password"
                    >
                    @error('password')
                        <p class="iba-login-error">{{ $message }}</p>
                    @enderror
                </div>

                <button type="submit" class="iba-login-btn">
                    Masuk ke Dashboard
                </button>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/login.js') }}"></script>
@endpush
