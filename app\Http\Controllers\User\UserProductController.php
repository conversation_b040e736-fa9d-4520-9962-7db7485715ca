<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\StoreStock;
use App\Models\StockMovement;
use App\Models\Store;
use App\Http\Requests\User\ProductAdjustmentRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserProductController extends Controller
{
    /**
     * Display store product management page
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')
                ->with('error', 'Anda tidak terdaftar di toko manapun');
        }

        // Get store stock with product information
        $query = StoreStock::where('store_id', $store->id)
            ->with('product');

        // Apply search filter if provided
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Apply time period filter (default to current month)
        $period = $request->get('period', 'month');
        $startDate = $this->getStartDateForPeriod($period);
        
        $storeStock = $query->orderBy('updated_at', 'desc')->paginate(10);

        // Get store statistics
        $stats = $this->getStoreProductStatistics($store);

        return view('user.products.index', compact('storeStock', 'stats', 'store'));
    }

    /**
     * Show stock adjustment form for a specific product
     */
    public function adjustForm($productId)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')
                ->with('error', 'Anda tidak terdaftar di toko manapun');
        }

        $product = Product::findOrFail($productId);
        
        // Get current store stock for this product
        $storeStock = StoreStock::where('store_id', $store->id)
            ->where('product_id', $product->id)
            ->first();

        if (!$storeStock) {
            return redirect()->route('user.products.index')
                ->with('error', 'Produk tidak ditemukan di stok toko Anda');
        }

        return view('user.products.adjust', compact('product', 'storeStock', 'store'));
    }

    /**
     * Process stock adjustment for store
     */
    public function adjust(ProductAdjustmentRequest $request, $productId)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')
                ->with('error', 'Anda tidak terdaftar di toko manapun');
        }

        $product = Product::findOrFail($productId);
        
        // Get current store stock
        $storeStock = StoreStock::where('store_id', $store->id)
            ->where('product_id', $product->id)
            ->first();

        if (!$storeStock) {
            return redirect()->route('user.products.index')
                ->with('error', 'Produk tidak ditemukan di stok toko Anda');
        }

        // Get validated data from request
        $adjustmentType = $request->input('adjustment_type');
        $quantity = $request->input('quantity', 0);
        $notes = $request->input('notes', '');

        // Log the adjustment attempt for audit purposes
        Log::info('Store product adjustment attempt', [
            'user_id' => $user->id,
            'store_id' => $store->id,
            'product_id' => $product->id,
            'adjustment_type' => $adjustmentType,
            'quantity' => $quantity,
            'current_stock' => $storeStock->quantity
        ]);

        // Process stock adjustment with audit logging and error handling
        try {
            DB::transaction(function () use ($storeStock, $product, $store, $adjustmentType, $quantity, $notes, $user) {
                $previousStock = $storeStock->quantity;

                // Calculate new stock
                switch ($adjustmentType) {
                    case 'subtract':
                        $newStock = $previousStock - $quantity;
                        $movementQuantity = $quantity;
                        break;
                    case 'set_zero':
                        $newStock = 0;
                        $movementQuantity = $previousStock; // Record how much was removed
                        break;
                }

                // Update store stock
                $storeStock->update(['quantity' => $newStock]);

                // Create stock movement record for audit
                StockMovement::create([
                    'product_id' => $product->id,
                    'type' => 'out',
                    'source' => 'store_adjustment',
                    'quantity' => $movementQuantity,
                    'previous_stock' => $previousStock,
                    'new_stock' => $newStock,
                    'reference_type' => 'store',
                    'reference_id' => $store->id,
                    'notes' => $notes ?: "Penyesuaian stok toko: " . match($adjustmentType) {
                        'subtract' => "Pengurangan {$quantity} unit",
                        'set_zero' => "Set stok menjadi habis (dari {$previousStock} unit)"
                    },
                    'created_by' => $user->id,
                ]);

                // Log successful adjustment
                Log::info('Store product adjustment completed successfully', [
                    'user_id' => $user->id,
                    'store_id' => $store->id,
                    'product_id' => $product->id,
                    'adjustment_type' => $adjustmentType,
                    'quantity' => $quantity,
                    'previous_stock' => $previousStock,
                    'new_stock' => $newStock
                ]);
            });

            $actionText = match($adjustmentType) {
                'subtract' => "dikurangi sebanyak {$quantity} unit",
                'set_zero' => "diatur menjadi habis (0 unit)"
            };

            $successMessage = "✅ Berhasil! Stok produk \"{$product->name}\" telah {$actionText}.\n\n";
            $successMessage .= "📊 Detail Perubahan:\n";
            $successMessage .= "• Stok sebelumnya: {$storeStock->quantity} unit\n";
            $successMessage .= "• Stok sekarang: " . ($adjustmentType === 'set_zero' ? '0' : ($storeStock->quantity - $quantity)) . " unit\n";
            $successMessage .= "• Waktu: " . now()->format('d/m/Y H:i') . " WIB";

            return redirect()->route('user.products.index')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            Log::error('Store product adjustment failed', [
                'user_id' => $user->id,
                'store_id' => $store->id,
                'product_id' => $product->id,
                'adjustment_type' => $adjustmentType,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);

            $errorMessage = "❌ Gagal menyesuaikan stok produk \"{$product->name}\".\n\n";
            $errorMessage .= "🔧 Silakan coba langkah berikut:\n";
            $errorMessage .= "• Refresh halaman dan coba lagi\n";
            $errorMessage .= "• Pastikan koneksi internet stabil\n";
            $errorMessage .= "• Hubungi administrator jika masalah berlanjut\n\n";
            $errorMessage .= "⏰ Waktu: " . now()->format('d/m/Y H:i') . " WIB";

            return redirect()->back()
                ->withInput()
                ->with('error', $errorMessage);
        }
    }

    /**
     * Quick action to set product stock to zero
     */
    public function setZero($productId)
    {
        $user = auth()->user();
        $store = $user->store;

        // Authorization check
        if (!$store) {
            return redirect()->route('user.dashboard')
                ->with('error', 'Anda tidak terdaftar di toko manapun');
        }

        $product = Product::findOrFail($productId);

        // Get current store stock with additional validation
        $storeStock = StoreStock::where('store_id', $store->id)
            ->where('product_id', $product->id)
            ->first();

        if (!$storeStock) {
            Log::warning('Attempt to adjust non-existent product stock', [
                'user_id' => $user->id,
                'store_id' => $store->id,
                'product_id' => $product->id
            ]);

            return redirect()->route('user.products.index')
                ->with('error', 'Produk tidak ditemukan di stok toko Anda');
        }

        if ($storeStock->quantity == 0) {
            $infoMessage = "💡 Informasi: Stok produk \"{$product->name}\" sudah habis (0 unit).\n\n";
            $infoMessage .= "Tidak perlu melakukan penyesuaian karena stok sudah kosong.";

            return redirect()->route('user.products.index')
                ->with('info', $infoMessage);
        }

        // Log the quick zero action attempt
        Log::info('Store product quick zero attempt', [
            'user_id' => $user->id,
            'store_id' => $store->id,
            'product_id' => $product->id,
            'current_stock' => $storeStock->quantity
        ]);

        // Process quick zero adjustment with error handling
        try {
            DB::transaction(function () use ($storeStock, $product, $store, $user) {
                $previousStock = $storeStock->quantity;

                // Set stock to zero
                $storeStock->update(['quantity' => 0]);

                // Create stock movement record for audit
                StockMovement::create([
                    'product_id' => $product->id,
                    'type' => 'out',
                    'source' => 'store_adjustment',
                    'quantity' => $previousStock,
                    'previous_stock' => $previousStock,
                    'new_stock' => 0,
                    'reference_type' => 'store',
                    'reference_id' => $store->id,
                    'notes' => "Aksi cepat: Set stok menjadi habis (dari {$previousStock} unit)",
                    'created_by' => $user->id,
                ]);

                // Log successful adjustment
                Log::info('Store product quick zero completed successfully', [
                    'user_id' => $user->id,
                    'store_id' => $store->id,
                    'product_id' => $product->id,
                    'previous_stock' => $previousStock
                ]);
            });

            $successMessage = "✅ Berhasil! Stok produk \"{$product->name}\" telah diatur menjadi habis.\n\n";
            $successMessage .= "📊 Detail Perubahan:\n";
            $successMessage .= "• Stok sebelumnya: {$storeStock->quantity} unit\n";
            $successMessage .= "• Stok sekarang: 0 unit\n";
            $successMessage .= "• Aksi: Tombol Habis (Quick Action)\n";
            $successMessage .= "• Waktu: " . now()->format('d/m/Y H:i') . " WIB";

            return redirect()->route('user.products.index')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            Log::error('Store product quick zero failed', [
                'user_id' => $user->id,
                'store_id' => $store->id,
                'product_id' => $product->id,
                'error' => $e->getMessage()
            ]);

            $errorMessage = "❌ Gagal mengatur stok produk \"{$product->name}\" menjadi habis.\n\n";
            $errorMessage .= "🔧 Silakan coba langkah berikut:\n";
            $errorMessage .= "• Refresh halaman dan coba lagi\n";
            $errorMessage .= "• Gunakan form penyesuaian manual jika tombol habis tidak berfungsi\n";
            $errorMessage .= "• Hubungi administrator jika masalah berlanjut\n\n";
            $errorMessage .= "⏰ Waktu: " . now()->format('d/m/Y H:i') . " WIB";

            return redirect()->route('user.products.index')
                ->with('error', $errorMessage);
        }
    }

    /**
     * Get store product statistics
     */
    private function getStoreProductStatistics($store)
    {
        $totalProducts = StoreStock::where('store_id', $store->id)->count();
        $totalStock = StoreStock::where('store_id', $store->id)->sum('quantity');
        $lowStockItems = StoreStock::where('store_id', $store->id)
            ->where('quantity', '<=', 5)
            ->count();
        $outOfStockItems = StoreStock::where('store_id', $store->id)
            ->where('quantity', 0)
            ->count();

        return [
            'total_products' => $totalProducts,
            'total_stock' => $totalStock,
            'low_stock_items' => $lowStockItems,
            'out_of_stock_items' => $outOfStockItems,
            'store_name' => $store->name
        ];
    }

    /**
     * Get start date for time period filter
     */
    private function getStartDateForPeriod($period)
    {
        switch ($period) {
            case 'week':
                return now()->startOfWeek();
            case 'day':
                return now()->startOfDay();
            case 'all':
                return null;
            case 'month':
            default:
                return now()->startOfMonth();
        }
    }
}
