{{-- 
    Reusable Confirmation Modal Component
    PT. Indah Berkah Abadi - Inventory System
    
    Usage:
    @include('user.components.confirmation-modal', [
        'modalId' => 'unique-modal-id',
        'title' => 'Modal Title',
        'type' => 'warning|danger|info', // Optional, defaults to 'info'
    ])
--}}

@php
    $modalType = $type ?? 'info';
    $modalId = $modalId ?? 'confirmation-modal';
@endphp

<!-- Modal Overlay -->
<div id="{{ $modalId }}" class="user-dashboard-modal-overlay" style="display: none;">
    <div class="user-dashboard-modal-container">
        <div class="user-dashboard-modal-content user-dashboard-modal-{{ $modalType }}">
            
            <!-- Modal Header -->
            <div class="user-dashboard-modal-header">
                <div class="user-dashboard-modal-icon-container">
                    @if($modalType === 'warning')
                        <svg class="user-dashboard-modal-icon user-dashboard-modal-icon-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    @elseif($modalType === 'danger')
                        <svg class="user-dashboard-modal-icon user-dashboard-modal-icon-danger" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    @else
                        <svg class="user-dashboard-modal-icon user-dashboard-modal-icon-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    @endif
                </div>
                
                <div class="user-dashboard-modal-header-content">
                    <h3 class="user-dashboard-modal-title" id="{{ $modalId }}-title">
                        {{ $title ?? 'Konfirmasi Tindakan' }}
                    </h3>
                    <button type="button" class="user-dashboard-modal-close" onclick="UserModalManager.closeModal('{{ $modalId }}')" aria-label="Tutup">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="user-dashboard-modal-body">
                <div class="user-dashboard-modal-message" id="{{ $modalId }}-message">
                    <!-- Message content will be populated by JavaScript -->
                </div>
                
                <!-- Product Information Section (for product-related modals) -->
                <div class="user-dashboard-modal-product-info" id="{{ $modalId }}-product-info" style="display: none;">
                    <div class="user-dashboard-modal-info-section">
                        <h4 class="user-dashboard-modal-info-title">Informasi Produk</h4>
                        <div class="user-dashboard-modal-info-grid">
                            <div class="user-dashboard-modal-info-item">
                                <span class="user-dashboard-modal-info-label">Nama Produk:</span>
                                <span class="user-dashboard-modal-info-value" id="{{ $modalId }}-product-name">-</span>
                            </div>
                            <div class="user-dashboard-modal-info-item">
                                <span class="user-dashboard-modal-info-label">Stok Saat Ini:</span>
                                <span class="user-dashboard-modal-info-value" id="{{ $modalId }}-current-stock">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Impact Preview Section -->
                <div class="user-dashboard-modal-impact-preview" id="{{ $modalId }}-impact-preview" style="display: none;">
                    <div class="user-dashboard-modal-info-section">
                        <h4 class="user-dashboard-modal-info-title">Pratinjau Perubahan</h4>
                        <div class="user-dashboard-modal-impact-grid">
                            <div class="user-dashboard-modal-impact-item">
                                <span class="user-dashboard-modal-impact-label">Stok Sebelum:</span>
                                <span class="user-dashboard-modal-impact-value" id="{{ $modalId }}-before-stock">-</span>
                            </div>
                            <div class="user-dashboard-modal-impact-item">
                                <span class="user-dashboard-modal-impact-label">Perubahan:</span>
                                <span class="user-dashboard-modal-impact-value user-dashboard-modal-impact-change" id="{{ $modalId }}-change-amount">-</span>
                            </div>
                            <div class="user-dashboard-modal-impact-item user-dashboard-modal-impact-result">
                                <span class="user-dashboard-modal-impact-label">Stok Setelah:</span>
                                <span class="user-dashboard-modal-impact-value" id="{{ $modalId }}-after-stock">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="user-dashboard-modal-footer">
                <button type="button" 
                        class="user-dashboard-modal-btn user-dashboard-modal-btn-secondary" 
                        onclick="UserModalManager.closeModal('{{ $modalId }}')"
                        id="{{ $modalId }}-cancel-btn">
                    <svg class="user-dashboard-modal-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Batal
                </button>
                
                <button type="button" 
                        class="user-dashboard-modal-btn user-dashboard-modal-btn-primary" 
                        onclick="UserModalManager.confirmAction('{{ $modalId }}')"
                        id="{{ $modalId }}-confirm-btn">
                    <svg class="user-dashboard-modal-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Konfirmasi
                </button>
            </div>
        </div>
    </div>
</div>
