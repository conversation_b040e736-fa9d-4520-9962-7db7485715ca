// Login Page JavaScript - Independent JS to prevent conflicts

// Add loading state to form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            const button = this.querySelector('.iba-login-btn');
            if (button) {
                button.disabled = true;
                button.textContent = 'Memproses...';
            }
        });
    }
});
