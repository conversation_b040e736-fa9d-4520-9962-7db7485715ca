/* PT. Indah Berkah Abadi - Analytics Sidebar CSS */
/* Distribution Comparison Analytics Components */
/* Mobile-First Responsive Design with proper z-index hierarchy */

/*
 * Z-INDEX HIERARCHY:
 * - Analytics sidebar: 900-999 (high priority, below main sidebar)
 * - Analytics overlay: 850-899 (medium-high priority)
 * - Analytics dropdowns: 50-99 (medium priority)
 * - Main sidebar navigation: 1000+ (highest priority)
 * - Main content: 1 (lowest priority)
 */

/* ===== ADMIN ANALYTICS SIDEBAR ===== */

.admin-dashboard-layout-with-analytics {
  position: relative;
  width: 100%;
}

.admin-dashboard-analytics-toggle-btn {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px 0 0 8px;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 950;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.admin-dashboard-analytics-toggle-btn:hover {
  background: #2563eb;
  transform: translateY(-50%) translateX(-2px);
}

.admin-dashboard-analytics-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 380px;
  height: 100vh;
  background: white;
  border-left: 1px solid #e5e7eb;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 900;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.admin-dashboard-analytics-sidebar.active {
  transform: translateX(0);
}

.admin-dashboard-analytics-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 850;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.admin-dashboard-analytics-overlay.active {
  opacity: 1;
  visibility: visible;
}

.admin-dashboard-analytics-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.admin-dashboard-analytics-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.admin-dashboard-analytics-toggle {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.admin-dashboard-analytics-toggle:hover {
  background: #e5e7eb;
  color: #374151;
}

.admin-dashboard-analytics-info {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #fafafa;
}

.admin-dashboard-filter-group {
  margin-bottom: 1rem;
}

.admin-dashboard-filter-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.admin-dashboard-filter-input,
.admin-dashboard-filter-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;
}

.admin-dashboard-filter-input:focus,
.admin-dashboard-filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-dashboard-filter-btn {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.625rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: background-color 0.2s ease;
}

.admin-dashboard-filter-btn:hover {
  background: #2563eb;
}

.admin-dashboard-analytics-summary {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.admin-dashboard-analytics-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.admin-dashboard-analytics-stat-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.admin-dashboard-analytics-stat-item {
  text-align: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 8px;
}

.admin-dashboard-analytics-stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.admin-dashboard-analytics-stat-value.admin-dashboard-analytics-stat-success {
  color: #059669;
}

.admin-dashboard-analytics-stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.admin-dashboard-analytics-discrepancy-indicators {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-dashboard-analytics-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.admin-dashboard-analytics-indicator-shortage {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.admin-dashboard-analytics-indicator-overage {
  background: #fefce8;
  color: #d97706;
  border: 1px solid #fef3c7;
}

.admin-dashboard-analytics-section {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.admin-dashboard-analytics-distributions-list {
  max-height: 400px;
  overflow-y: auto;
}

.admin-dashboard-analytics-distribution-item {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
}

.admin-dashboard-analytics-distribution-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.admin-dashboard-analytics-distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.admin-dashboard-analytics-distribution-product {
  font-weight: 500;
  color: #1f2937;
  flex: 1;
}

.admin-dashboard-analytics-distribution-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.admin-dashboard-analytics-distribution-badge.perfect {
  background: #dcfce7;
  color: #166534;
}

.admin-dashboard-analytics-distribution-badge.shortage {
  background: #fef2f2;
  color: #dc2626;
}

.admin-dashboard-analytics-distribution-badge.overage {
  background: #fefce8;
  color: #d97706;
}

.admin-dashboard-analytics-distribution-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.admin-dashboard-analytics-distribution-quantities {
  margin-bottom: 0.25rem;
}

.admin-dashboard-analytics-distribution-notes {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

.admin-dashboard-analytics-ranking-list,
.admin-dashboard-analytics-problem-list {
  max-height: 250px;
  overflow-y: auto;
}

.admin-dashboard-analytics-ranking-item,
.admin-dashboard-analytics-problem-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.admin-dashboard-analytics-ranking-position {
  width: 24px;
  height: 24px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.admin-dashboard-analytics-ranking-content {
  flex: 1;
}

.admin-dashboard-analytics-ranking-name,
.admin-dashboard-analytics-problem-name {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.admin-dashboard-analytics-ranking-stats,
.admin-dashboard-analytics-problem-stats {
  font-size: 0.75rem;
  color: #6b7280;
}

.admin-dashboard-analytics-ranking-accuracy {
  color: #059669;
  font-weight: 600;
}

.admin-dashboard-analytics-problem-rate {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.admin-dashboard-analytics-problem-rate-high {
  background: #fef2f2;
  color: #dc2626;
}

.admin-dashboard-analytics-empty {
  text-align: center;
  padding: 2rem 1rem;
  color: #6b7280;
}

/* ===== USER ANALYTICS SIDEBAR ===== */

.user-dashboard-layout-with-analytics {
  position: relative;
  width: 100%;
}

.user-dashboard-analytics-toggle-btn {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background: #059669;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px 0 0 8px;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 950;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.user-dashboard-analytics-toggle-btn:hover {
  background: #047857;
  transform: translateY(-50%) translateX(-2px);
}

.user-dashboard-analytics-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 360px;
  height: 100vh;
  background: white;
  border-left: 1px solid #e5e7eb;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 900;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.user-dashboard-analytics-sidebar.active {
  transform: translateX(0);
}

.user-dashboard-analytics-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 850;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.user-dashboard-analytics-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* User Analytics Components - Similar structure but with user-dashboard-* prefixes */
.user-dashboard-analytics-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f0fdf4;
}

.user-dashboard-analytics-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.user-dashboard-analytics-toggle {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.user-dashboard-analytics-toggle:hover {
  background: #e5e7eb;
  color: #374151;
}

.user-dashboard-analytics-info {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #fafafa;
}

.user-dashboard-filter-group {
  margin-bottom: 1rem;
}

.user-dashboard-filter-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.user-dashboard-filter-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;
}

.user-dashboard-filter-input:focus {
  outline: none;
  border-color: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.user-dashboard-filter-btn {
  width: 100%;
  background: #059669;
  color: white;
  border: none;
  padding: 0.625rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: background-color 0.2s ease;
}

.user-dashboard-filter-btn:hover {
  background: #047857;
}

/* User Analytics Summary */
.user-dashboard-analytics-summary {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.user-dashboard-analytics-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.user-dashboard-analytics-stat-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.user-dashboard-analytics-stat-item {
  text-align: center;
  padding: 0.75rem;
  background: #f0fdf4;
  border-radius: 8px;
}

.user-dashboard-analytics-stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.user-dashboard-analytics-stat-value.user-dashboard-analytics-stat-success {
  color: #059669;
}

.user-dashboard-analytics-stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* User Analytics Discrepancy Indicators */
.user-dashboard-analytics-discrepancy-indicators {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.user-dashboard-analytics-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.user-dashboard-analytics-indicator-shortage {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.user-dashboard-analytics-indicator-overage {
  background: #fefce8;
  color: #d97706;
  border: 1px solid #fef3c7;
}

/* User Analytics Sections */
.user-dashboard-analytics-section {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.user-dashboard-analytics-distributions-list {
  max-height: 400px;
  overflow-y: auto;
}

.user-dashboard-analytics-distribution-item {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
}

.user-dashboard-analytics-distribution-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.user-dashboard-analytics-distribution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.user-dashboard-analytics-distribution-product {
  font-weight: 500;
  color: #1f2937;
  flex: 1;
}

.user-dashboard-analytics-distribution-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.user-dashboard-analytics-distribution-badge.perfect {
  background: #dcfce7;
  color: #166534;
}

.user-dashboard-analytics-distribution-badge.shortage {
  background: #fef2f2;
  color: #dc2626;
}

.user-dashboard-analytics-distribution-badge.overage {
  background: #fefce8;
  color: #d97706;
}

.user-dashboard-analytics-distribution-details {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.user-dashboard-analytics-distribution-quantities {
  margin-bottom: 0.25rem;
}

.user-dashboard-analytics-distribution-notes {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

.user-dashboard-analytics-trend-accuracy {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.user-dashboard-analytics-trend-excellent {
  background: #dcfce7;
  color: #166534;
}

.user-dashboard-analytics-trend-good {
  background: #fef3c7;
  color: #92400e;
}

.user-dashboard-analytics-trend-poor {
  background: #fef2f2;
  color: #991b1b;
}

.user-dashboard-analytics-trend-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.user-dashboard-analytics-trend-progress {
  height: 100%;
  background: #059669;
  transition: width 0.3s ease;
}

.user-dashboard-analytics-perfect-badge {
  color: #059669;
}

.user-dashboard-analytics-issue-details,
.user-dashboard-analytics-trend-stats,
.user-dashboard-analytics-perfect-details {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.user-dashboard-analytics-issue-notes {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

.user-dashboard-analytics-actions {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.user-dashboard-analytics-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #059669;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.user-dashboard-analytics-action-btn:hover {
  background: #047857;
}

.user-dashboard-analytics-empty {
  text-align: center;
  padding: 2rem 1rem;
  color: #6b7280;
}

/* ===== MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
  .admin-dashboard-analytics-sidebar,
  .user-dashboard-analytics-sidebar {
    width: 100%;
    max-width: 100vw;
  }

  .admin-dashboard-analytics-toggle-btn,
  .user-dashboard-analytics-toggle-btn {
    writing-mode: horizontal-tb;
    text-orientation: initial;
    top: auto;
    bottom: 20px;
    right: 20px;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    padding: 0;
    transform: none;
  }

  .admin-dashboard-analytics-toggle-btn:hover,
  .user-dashboard-analytics-toggle-btn:hover {
    transform: scale(1.05);
  }

  .admin-dashboard-analytics-toggle-btn span,
  .user-dashboard-analytics-toggle-btn span {
    display: none;
  }

  .admin-dashboard-analytics-stat-grid,
  .user-dashboard-analytics-stat-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .admin-dashboard-analytics-header,
  .user-dashboard-analytics-header,
  .admin-dashboard-analytics-filters,
  .user-dashboard-analytics-filters,
  .admin-dashboard-analytics-summary,
  .user-dashboard-analytics-summary,
  .admin-dashboard-analytics-section,
  .user-dashboard-analytics-section,
  .user-dashboard-analytics-actions {
    padding: 1rem;
  }

  .admin-dashboard-analytics-issues-list,
  .user-dashboard-analytics-issues-list,
  .user-dashboard-analytics-trend-list,
  .user-dashboard-analytics-perfect-list,
  .admin-dashboard-analytics-ranking-list,
  .admin-dashboard-analytics-problem-list {
    max-height: 200px;
  }
}

@media (max-width: 480px) {
  .admin-dashboard-analytics-toggle-btn,
  .user-dashboard-analytics-toggle-btn {
    width: 48px;
    height: 48px;
    bottom: 16px;
    right: 16px;
  }

  .admin-dashboard-analytics-header,
  .user-dashboard-analytics-header,
  .admin-dashboard-analytics-filters,
  .user-dashboard-analytics-filters,
  .admin-dashboard-analytics-summary,
  .user-dashboard-analytics-summary,
  .admin-dashboard-analytics-section,
  .user-dashboard-analytics-section,
  .user-dashboard-analytics-actions {
    padding: 0.75rem;
  }

  .admin-dashboard-analytics-stat-value,
  .user-dashboard-analytics-stat-value {
    font-size: 1.125rem;
  }

  .admin-dashboard-analytics-issue-item,
  .user-dashboard-analytics-issue-item,
  .user-dashboard-analytics-trend-item,
  .user-dashboard-analytics-perfect-item,
  .admin-dashboard-analytics-ranking-item,
  .admin-dashboard-analytics-problem-item {
    padding: 0.5rem;
  }
}
