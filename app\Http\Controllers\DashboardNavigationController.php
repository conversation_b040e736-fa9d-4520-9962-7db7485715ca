<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardNavigationController extends Controller
{
    /**
     * Show dashboard selection page for admins
     */
    public function adminDashboardSelection()
    {
        // Only allow admins to access this page
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized access');
        }

        return view('navigation.admin-dashboard-selection');
    }

    /**
     * Show return to dashboard page for users
     */
    public function userDashboardReturn()
    {
        // Only allow authenticated users
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        return view('navigation.user-dashboard-return');
    }

    /**
     * Handle dashboard navigation based on selection
     */
    public function navigateToDashboard(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $dashboardType = $request->input('dashboard_type');

        // Validate dashboard type and user permissions
        switch ($dashboardType) {
            case 'admin':
                if ($user->isAdmin()) {
                    return redirect()->route('admin.dashboard');
                }
                abort(403, 'Unauthorized access to admin dashboard');
                break;

            case 'user':
                return redirect()->route('user.dashboard');
                break;

            default:
                // Default behavior based on user role
                if ($user->isAdmin()) {
                    return redirect()->route('admin.dashboard');
                }
                return redirect()->route('user.dashboard');
        }
    }
}
