@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON> - Indah Berkah Abadi')

@section('content')
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Ke<PERSON><PERSON></h1>
                    <p class="text-gray-600 mt-1">Kelola retur produk dari toko ke supplier</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <!-- <a href="{{ route('admin.returns.create') }}" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Buat Retur Baru
                    </a> -->
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <!-- <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['total'] ?? 0) }}</p>
                    <p class="admin-dashboard-stat-label">Total Retur</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-yellow-100 text-yellow-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['requested'] ?? 0) }}</p>
                    <p class="admin-dashboard-stat-label">Menunggu</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-green-100 text-green-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['approved'] ?? 0) }}</p>
                    <p class="admin-dashboard-stat-label">Disetujui</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['completed'] ?? 0) }}</p>
                    <p class="admin-dashboard-stat-label">Selesai</p>
                </div>
            </div>
        </div>
    </div> -->

    <!-- Filters -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" action="{{ route('admin.returns.index') }}" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari Retur</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Cari berdasarkan produk, toko, atau supplier..."
                           class="admin-dashboard-input">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="admin-dashboard-select">
                        <option value="">Semua Status</option>
                        <option value="requested" {{ request('status') === 'requested' ? 'selected' : '' }}>Menunggu</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Disetujui</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Ditolak</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Selesai</option>
                    </select>
                </div>
                <div>
                    <label for="month" class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <select id="month" name="month" class="admin-dashboard-select">
                        @foreach($availableMonths as $month)
                            <option value="{{ $month['value'] }}" {{ ($filterMonth ?? 'all') === $month['value'] ? 'selected' : '' }}>
                                {{ $month['label'] }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="flex space-x-2">
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Filter
                    </button>
                    @if(request()->hasAny(['search', 'status', 'month']))
                    <a href="{{ route('admin.returns.index') }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        Reset
                    </a>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Retur</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <!-- <th class="px-6 py-3">Toko</th> -->
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Supplier</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Tanggal Retur</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($returns ?? [] as $return)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->product->name ?? 'N/A' }}</div>
                                <div class="text-sm text-gray-500">{{ $return->product->category->name ?? 'Tanpa Kategori' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $return->supplier->name ?? 'N/A' }}</div>
                                <div class="text-xs text-gray-500">{{ $return->supplier->contact_person ?? '' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 font-medium">{{ number_format($return->quantity ?? 0) }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">{{ $return->return_date ? $return->return_date->format('d/m/Y') : '-' }}</div>
                                @if($return->processed_date)
                                <div class="text-xs text-gray-500">Diproses: {{ $return->processed_date->format('d/m/Y') }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($return->status === 'requested') bg-yellow-100 text-yellow-800
                                    @elseif($return->status === 'approved') bg-green-100 text-green-800
                                    @elseif($return->status === 'rejected') bg-red-100 text-red-800
                                    @elseif($return->status === 'completed') bg-blue-100 text-blue-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    @if($return->status === 'requested') Menunggu
                                    @elseif($return->status === 'approved') Disetujui
                                    @elseif($return->status === 'rejected') Ditolak
                                    @elseif($return->status === 'completed') Selesai
                                    @else {{ ucfirst($return->status ?? '') }}
                                    @endif
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.returns.show', $return) }}"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Lihat
                                    </a>
                                    @if($return->status === 'approved')
                                    @if($return->isFromStore() && !$return->supplier_id)
                                    <button onclick="openForwardToSupplierModal('{{ $return->id }}', '{{ $return->product->name }}')"
                                            class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                        Teruskan ke Supplier
                                    </button>
                                    @endif
                                    <button onclick="openCompleteModal('{{ $return->id }}')"
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Selesaikan
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum Ada Retur</p>
                                    <p class="mb-4">Belum ada retur yang perlu dikelola</p>
                                    <!-- <a href="{{ route('admin.returns.create') }}" class="admin-dashboard-btn admin-dashboard-btn-primary">
                                        Buat Retur Baru
                                    </a> -->
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(isset($returns) && $returns->hasPages())
            <div class="mt-6">
                {{ $returns->links() }}
            </div>
            @endif
        </div>
    </div>
</div>





<!-- Forward to Supplier Modal -->
<div id="forwardToSupplierModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Teruskan ke Supplier</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeForwardToSupplierModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="forwardToSupplierForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Teruskan Retur ke Supplier</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Pilih supplier untuk meneruskan retur produk <strong id="forwardProductName"></strong>.
                        </p>
                        <div class="space-y-4">
                            <div>
                                <label for="forward_supplier_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Supplier <span class="text-red-500">*</span>
                                </label>
                                <select id="forward_supplier_id" name="supplier_id" class="admin-dashboard-select" required>
                                    <option value="">Pilih Supplier</option>
                                    @foreach(\App\Models\Supplier::active()->orderBy('name')->get() as $supplier)
                                        <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label for="forward_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                    Catatan (Opsional)
                                </label>
                                <textarea id="forward_notes" name="admin_notes" rows="3"
                                          class="admin-dashboard-textarea"
                                          placeholder="Tambahkan catatan untuk supplier..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeForwardToSupplierModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Teruskan ke Supplier
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>


// Forward to Supplier Modal Functions
function openForwardToSupplierModal(returnId, productName) {
    const modal = document.getElementById('forwardToSupplierModal');
    const form = document.getElementById('forwardToSupplierForm');
    const productNameElement = document.getElementById('forwardProductName');

    form.action = `/admin/returns/${returnId}/forward-to-supplier`;
    productNameElement.textContent = productName;
    modal.classList.add('active');
}

function closeForwardToSupplierModal() {
    const modal = document.getElementById('forwardToSupplierModal');
    modal.classList.remove('active');
    document.getElementById('forwardToSupplierForm').reset();
}

// Close modals when clicking outside

document.getElementById('forwardToSupplierModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeForwardToSupplierModal();
    }
});
</script>
@endpush
@endsection
