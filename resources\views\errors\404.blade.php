<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Halaman Tidak Ditemukan - PT. Indah Berkah Abadi</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%23166534'/><text x='50' y='65' font-family='Arial,sans-serif' font-size='36' font-weight='bold' text-anchor='middle' fill='white'>IBA</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1a202c;
            line-height: 1.6;
        }

        .error-container {
            max-width: 600px;
            width: 90%;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            text-align: center;
        }

        .error-header {
            background: linear-gradient(135deg, #166534 0%, #15803d 100%);
            color: white;
            padding: 2rem;
        }

        .company-logo {
            width: 64px;
            height: 64px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 24px;
            font-weight: bold;
        }

        .error-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .error-subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .error-content {
            padding: 2rem;
        }

        .error-code {
            font-size: 4rem;
            font-weight: 800;
            color: #dc2626;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(220, 38, 38, 0.1);
        }

        .error-message {
            font-size: 1.125rem;
            color: #374151;
            margin-bottom: 1.5rem;
        }

        .error-description {
            color: #6b7280;
            margin-bottom: 2rem;
            line-height: 1.7;
        }

        .error-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #166534 0%, #15803d 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #14532d 0%, #166534 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(22, 101, 52, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            transform: translateY(-1px);
        }

        .btn svg {
            width: 16px;
            height: 16px;
            margin-right: 0.5rem;
        }

        .error-footer {
            background: #f9fafb;
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
            font-size: 0.875rem;
            color: #6b7280;
        }

        @media (min-width: 640px) {
            .error-actions {
                flex-direction: row;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .error-container {
                width: 95%;
                margin: 1rem;
            }
            
            .error-header {
                padding: 1.5rem;
            }
            
            .error-content {
                padding: 1.5rem;
            }
            
            .error-code {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-header">
            <div class="company-logo">IBA</div>
            <h1 class="error-title">PT. Indah Berkah Abadi</h1>
            <p class="error-subtitle">Sistem Inventori</p>
        </div>
        
        <div class="error-content">
            <div class="error-code">404</div>
            <h2 class="error-message">Halaman Tidak Ditemukan</h2>
            <p class="error-description">
                Maaf, halaman yang Anda cari tidak dapat ditemukan. 
                Halaman mungkin telah dipindahkan, dihapus, atau URL yang Anda masukkan salah. 
                Silakan periksa kembali alamat atau kembali ke halaman utama.
            </p>
            
            <div class="error-actions">
                @auth
                    @if(auth()->user()->isAdmin())
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-primary">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                            </svg>
                            Dashboard Admin
                        </a>
                    @else
                        <a href="{{ route('user.dashboard') }}" class="btn btn-primary">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                            </svg>
                            Dashboard Toko
                        </a>
                    @endif
                @else
                    <a href="{{ route('login') }}" class="btn btn-primary">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                        </svg>
                        Masuk ke Sistem
                    </a>
                @endauth
                
                <a href="{{ route('home') }}" class="btn btn-secondary">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Beranda
                </a>
            </div>
        </div>
        
        <div class="error-footer">
            <p>&copy; {{ date('Y') }} PT. Indah Berkah Abadi. Semua hak dilindungi.</p>
        </div>
    </div>
</body>
</html>
