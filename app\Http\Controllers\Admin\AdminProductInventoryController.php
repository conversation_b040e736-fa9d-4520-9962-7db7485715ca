<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Store;
use App\Models\StoreStock;
use App\Models\Distribution;
use App\Models\StockMovement;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AdminProductInventoryController extends Controller
{
    /**
     * Display product inventory management page
     */
    public function index(Request $request)
    {
        // Get all products for dropdown
        $products = Product::orderBy('name')->get();

        $selectedProduct = null;
        $productStores = collect();
        $productStats = [];

        // If product is selected, get its store data
        if ($request->filled('product_id')) {
            $selectedProduct = Product::find($request->get('product_id'));
            
            if ($selectedProduct) {
                // Get stores that have this product
                $productStores = $this->getProductStores($selectedProduct->id, $request);
                $productStats = $this->getProductStats($selectedProduct->id);
            }
        }

        return view('admin.product-inventory.index', compact(
            'products',
            'selectedProduct',
            'productStores',
            'productStats'
        ));
    }

    /**
     * Get stores for a specific product with stock and distribution data
     */
    private function getProductStores($productId, Request $request)
    {
        // Get all stores that have received this product
        $query = User::where('role', 'user')
            ->with(['store'])
            ->whereHas('store.distributions', function ($q) use ($productId) {
                $q->where('product_id', $productId);
            });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhereHas('store', function ($sq) use ($search) {
                      $sq->where('name', 'like', "%{$search}%")
                        ->orWhere('location', 'like', "%{$search}%");
                  });
            });
        }

        $stores = $query->get();

        // Transform stores to include additional data
        return $stores->map(function ($user) use ($productId) {
            $store = $user->store;
            
            // Get current stock for this product at this store
            $storeStock = StoreStock::where('store_id', $store->id)
                ->where('product_id', $productId)
                ->first();
            $currentStock = $storeStock ? $storeStock->quantity : 0;
            
            // Get distribution statistics
            $distributions = Distribution::where('store_id', $store->id)
                ->where('product_id', $productId)
                ->get();
                
            $totalDistributed = $distributions->sum('quantity');
            $totalReceived = $distributions->where('confirmed', true)->sum('received_quantity');
            $pendingDistributions = $distributions->where('confirmed', false)->count();
            
            // Get last distribution
            $lastDistribution = $distributions->sortByDesc('date_distributed')->first();
            
            return [
                'store_id' => $store->id,
                'store_name' => $store->name,
                'store_location' => $store->location,
                'user_name' => $user->name,
                'user_email' => $user->email,
                'current_stock' => $currentStock,
                'total_distributed' => $totalDistributed,
                'total_received' => $totalReceived,
                'pending_distributions' => $pendingDistributions,
                'last_distribution' => $lastDistribution,
                'stock_status' => $this->getStockStatus($currentStock),
                'store_stock_id' => $storeStock ? $storeStock->id : null
            ];
        })->sortBy('store_name')->values();
    }

    /**
     * Get statistics for a specific product
     */
    private function getProductStats($productId)
    {
        $totalStores = User::where('role', 'user')
            ->whereHas('store.distributions', function ($q) use ($productId) {
                $q->where('product_id', $productId);
            })->count();

        $totalStoreStock = StoreStock::where('product_id', $productId)->sum('quantity');
        
        $pendingDistributions = Distribution::where('product_id', $productId)
            ->where('confirmed', false)
            ->count();
            
        $totalDistributions = Distribution::where('product_id', $productId)->count();

        return [
            'total_stores' => $totalStores,
            'total_store_stock' => $totalStoreStock,
            'pending_distributions' => $pendingDistributions,
            'total_distributions' => $totalDistributions
        ];
    }

    /**
     * Get stock status based on quantity
     */
    private function getStockStatus($quantity)
    {
        if ($quantity <= 0) {
            return ['status' => 'out', 'label' => 'Habis', 'class' => 'bg-red-100 text-red-800'];
        } elseif ($quantity <= 5) {
            return ['status' => 'low', 'label' => 'Sedikit', 'class' => 'bg-yellow-100 text-yellow-800'];
        } else {
            return ['status' => 'good', 'label' => 'Baik', 'class' => 'bg-green-100 text-green-800'];
        }
    }

    /**
     * Show stock adjustment form for a specific product at a store
     */
    public function adjustForm(Request $request)
    {
        $storeId = $request->get('store_id');
        $productId = $request->get('product_id');
        
        $store = Store::findOrFail($storeId);
        $product = Product::findOrFail($productId);
        
        // Get current store stock
        $storeStock = StoreStock::where('store_id', $storeId)
            ->where('product_id', $productId)
            ->first();
            
        $currentStock = $storeStock ? $storeStock->quantity : 0;
        
        // Get recent distribution history
        $recentDistributions = Distribution::where('store_id', $storeId)
            ->where('product_id', $productId)
            ->with('product')
            ->orderBy('date_distributed', 'desc')
            ->limit(5)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'store' => $store,
                'product' => $product,
                'current_stock' => $currentStock,
                'recent_distributions' => $recentDistributions->map(function ($dist) {
                    return [
                        'id' => $dist->id,
                        'quantity' => $dist->quantity,
                        'received_quantity' => $dist->received_quantity,
                        'date_distributed' => $dist->date_distributed->format('d M Y'),
                        'confirmed' => $dist->confirmed,
                        'status_label' => $dist->status_label
                    ];
                })
            ]
        ]);
    }

    /**
     * Process stock adjustment for a specific product at a store
     */
    public function adjustStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'store_id' => 'required|exists:stores,id',
            'product_id' => 'required|exists:products,id',
            'adjustment_type' => 'required|in:add,subtract,set',
            'quantity' => 'required|integer|min:0',
            'notes' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $validator->errors()
            ], 422);
        }

        $storeId = $request->get('store_id');
        $productId = $request->get('product_id');
        $adjustmentType = $request->get('adjustment_type');
        $quantity = $request->get('quantity');
        $notes = $request->get('notes', '');

        $store = Store::findOrFail($storeId);
        $product = Product::findOrFail($productId);

        try {
            DB::transaction(function () use ($store, $product, $adjustmentType, $quantity, $notes) {
                // Find or create store stock entry
                $storeStock = StoreStock::firstOrCreate(
                    [
                        'store_id' => $store->id,
                        'product_id' => $product->id
                    ],
                    ['quantity' => 0]
                );

                $previousStock = $storeStock->quantity;
                $newStock = $previousStock;

                // Calculate new stock based on adjustment type
                switch ($adjustmentType) {
                    case 'add':
                        $newStock = $previousStock + $quantity;
                        break;
                    case 'subtract':
                        $newStock = max(0, $previousStock - $quantity);
                        break;
                    case 'set':
                        $newStock = $quantity;
                        break;
                }

                // Update store stock
                $storeStock->update(['quantity' => $newStock]);

                // Log stock movement
                StockMovement::create([
                    'product_id' => $product->id,
                    'type' => $newStock > $previousStock ? 'in' : 'out',
                    'source' => 'store_adjustment',
                    'quantity' => $newStock - $previousStock,
                    'previous_stock' => $previousStock,
                    'new_stock' => $newStock,
                    'reference_type' => 'store',
                    'reference_id' => $store->id,
                    'notes' => $notes ?: "Penyesuaian stok produk {$product->name} di toko {$store->name} oleh admin",
                    'created_by' => auth()->id(),
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => 'Stok berhasil disesuaikan'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyesuaikan stok: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get distribution history for a specific product at a store
     */
    public function getDistributionHistory(Request $request)
    {
        $storeId = $request->get('store_id');
        $productId = $request->get('product_id');
        
        $distributions = Distribution::where('store_id', $storeId)
            ->where('product_id', $productId)
            ->with(['product', 'store'])
            ->orderBy('date_distributed', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $distributions->items(),
            'pagination' => [
                'current_page' => $distributions->currentPage(),
                'last_page' => $distributions->lastPage(),
                'per_page' => $distributions->perPage(),
                'total' => $distributions->total()
            ]
        ]);
    }
}
