/* Homepage Specific Styles - Independent CSS to prevent conflicts */
.iba-homepage-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.iba-homepage-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    position: sticky;
    top: 0;
    z-index: 50;
    transition: all 0.3s ease;
}

.iba-homepage-header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.iba-homepage-logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.iba-homepage-logo {
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
    transition: transform 0.3s ease;
}

.iba-homepage-logo:hover {
    transform: translateY(-2px);
}

.iba-homepage-logo-text {
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    letter-spacing: -0.025em;
}

.iba-homepage-company-name {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1e293b;
    letter-spacing: -0.025em;
}

.iba-homepage-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.iba-homepage-user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(37, 99, 235, 0.1);
    border-radius: 0.75rem;
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.iba-homepage-user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.iba-homepage-user-details h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.iba-homepage-user-details p {
    font-size: 0.75rem;
    color: #64748b;
    margin: 0;
}

.iba-homepage-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.iba-homepage-btn-primary {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
}

.iba-homepage-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5);
}

.iba-homepage-btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.iba-homepage-btn-secondary:hover {
    background: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

/* Hero Section */
.iba-homepage-hero {
    padding: 4rem 0 6rem;
    text-align: center;
}

.iba-homepage-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.iba-homepage-hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(37, 99, 235, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #2563eb;
    margin-bottom: 2rem;
}

.iba-homepage-hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
}

.iba-homepage-hero-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
}

.iba-homepage-hero-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Stats Section */
.iba-homepage-stats {
    padding: 4rem 0;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
}

.iba-homepage-stats-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.iba-homepage-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.iba-homepage-stats-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.iba-homepage-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.iba-homepage-stats-number {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.iba-homepage-stats-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Features Section */
.iba-homepage-features {
    padding: 6rem 0;
    background: white;
}

.iba-homepage-features-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.iba-homepage-features-header {
    text-align: center;
    margin-bottom: 4rem;
}

.iba-homepage-features-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 1rem;
}

.iba-homepage-features-subtitle {
    font-size: 1.125rem;
    color: #64748b;
    max-width: 48rem;
    margin: 0 auto;
}

.iba-homepage-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.iba-homepage-feature-card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.iba-homepage-feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.iba-homepage-feature-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.iba-homepage-feature-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.75rem;
}

.iba-homepage-feature-description {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.iba-homepage-feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.iba-homepage-feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #475569;
}

.iba-homepage-feature-item svg {
    margin-right: 0.5rem;
    flex-shrink: 0;
}

/* Footer */
.iba-homepage-footer {
    background: #1e293b;
    color: white;
    padding: 3rem 0;
}

.iba-homepage-footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    text-align: center;
}

.iba-homepage-footer-logo-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.iba-homepage-footer-title {
    font-size: 1.5rem;
    font-weight: 700;
}

.iba-homepage-footer-description {
    color: #94a3b8;
    margin-bottom: 2rem;
    max-width: 32rem;
    margin-left: auto;
    margin-right: auto;
}

.iba-homepage-footer-divider {
    border-top: 1px solid #334155;
    padding-top: 2rem;
}

.iba-homepage-footer-copyright {
    color: #64748b;
    font-size: 0.875rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .iba-homepage-header-container {
        padding: 1rem;
    }

    .iba-homepage-company-name {
        font-size: 1.25rem;
    }

    .iba-homepage-logo {
        width: 3rem;
        height: 3rem;
    }

    .iba-homepage-logo-text {
        font-size: 1rem;
    }

    .iba-homepage-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.8rem;
    }

    .iba-homepage-user-info {
        padding: 0.5rem 0.75rem;
    }

    .iba-homepage-user-avatar {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .iba-homepage-hero-title {
        font-size: 2.5rem;
    }

    .iba-homepage-hero-subtitle {
        font-size: 1rem;
    }

    .iba-homepage-hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .iba-homepage-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .iba-homepage-stats-card {
        padding: 1.5rem;
    }

    .iba-homepage-stats-number {
        font-size: 2rem;
    }

    .iba-homepage-features-grid {
        grid-template-columns: 1fr;
    }

    .iba-homepage-features-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .iba-homepage-header-actions {
        gap: 0.5rem;
    }

    .iba-homepage-btn {
        padding: 0.625rem 1rem;
        font-size: 0.75rem;
    }

    .iba-homepage-stats-grid {
        grid-template-columns: 1fr;
    }

    .iba-homepage-hero-title {
        font-size: 2rem;
    }

    .iba-homepage-features-title {
        font-size: 1.75rem;
    }
}
