@extends('layouts.user')

@section('title', '<PERSON><PERSON><PERSON> Produk - Dashboard Toko')

@push('styles')
<!-- Independent CSS for User Dashboard Product Management -->
<link rel="stylesheet" href="{{ asset('css/user-dashboard-forms.css') }}">
<link rel="stylesheet" href="{{ asset('css/user-dashboard-product-management.css') }}">
@endpush

@section('content')
<div class="user-dashboard-container">
    <!-- Success Messages Display -->
    @if (session('success'))
        <div class="user-dashboard-product-success-container">
            <div class="user-dashboard-product-success-header">
                <svg class="user-dashboard-product-success-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h4 class="user-dashboard-product-success-title">Berhasil</h4>
            </div>
            <p class="user-dashboard-product-success-message">{{ session('success') }}</p>
        </div>
    @endif

    <!-- Info Messages Display -->
    @if (session('info'))
        <div class="user-dashboard-product-info-container">
            <div class="user-dashboard-product-info-header">
                <svg class="user-dashboard-product-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h4 class="user-dashboard-product-info-title">Informasi</h4>
            </div>
            <p class="user-dashboard-product-info-message">{{ session('info') }}</p>
        </div>
    @endif

    <!-- Error Messages Display -->
    @if (session('error'))
        <div class="user-dashboard-product-error-container">
            <div class="user-dashboard-product-error-header">
                <svg class="user-dashboard-product-error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h4 class="user-dashboard-product-error-title">Terjadi Kesalahan</h4>
            </div>
            <p class="user-dashboard-product-error-item">{{ session('error') }}</p>
        </div>
    @endif
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Kelola Produk</h1>
            <p class="user-dashboard-header-subtitle">Kelola stok produk di {{ $stats['store_name'] }}</p>
        </div>
        <div class="user-dashboard-header-actions">
            <a href="{{ route('user.dashboard') }}" class="user-dashboard-btn-secondary user-dashboard-btn-sm">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m0 7h18"></path>
                </svg>
                Kembali
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="user-dashboard-product-stats-grid">
        <div class="user-dashboard-product-stat-card">
            <div class="user-dashboard-product-stat-header">
                <h3 class="user-dashboard-product-stat-title">Total Produk</h3>
                <svg class="user-dashboard-product-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div class="user-dashboard-product-stat-value">{{ number_format($stats['total_products']) }}</div>
            <div class="user-dashboard-product-stat-description">Jenis produk</div>
        </div>

        <div class="user-dashboard-product-stat-card">
            <div class="user-dashboard-product-stat-header">
                <h3 class="user-dashboard-product-stat-title">Total Stok</h3>
                <svg class="user-dashboard-product-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
            </div>
            <div class="user-dashboard-product-stat-value">{{ number_format($stats['total_stock']) }}</div>
            <div class="user-dashboard-product-stat-description">Unit tersedia</div>
        </div>

        <div class="user-dashboard-product-stat-card user-dashboard-product-stat-warning">
            <div class="user-dashboard-product-stat-header">
                <h3 class="user-dashboard-product-stat-title">Stok Rendah</h3>
                <svg class="user-dashboard-product-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="user-dashboard-product-stat-value">{{ number_format($stats['low_stock_items']) }}</div>
            <div class="user-dashboard-product-stat-description">≤ 5 unit</div>
        </div>

        <div class="user-dashboard-product-stat-card user-dashboard-product-stat-danger">
            <div class="user-dashboard-product-stat-header">
                <h3 class="user-dashboard-product-stat-title">Habis</h3>
                <svg class="user-dashboard-product-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="user-dashboard-product-stat-value">{{ number_format($stats['out_of_stock_items']) }}</div>
            <div class="user-dashboard-product-stat-description">0 unit</div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="user-dashboard-product-search-wrapper">
        <form method="GET" action="{{ route('user.products.index') }}" class="user-dashboard-product-search-form">
            <div class="user-dashboard-product-input-group">
                <label class="user-dashboard-product-label">Cari Produk</label>
                <input type="text" 
                       name="search" 
                       value="{{ request('search') }}"
                       placeholder="Masukkan nama produk..."
                       class="user-dashboard-form-input-enhanced">
            </div>
            <div class="user-dashboard-product-input-group">
                <button type="submit" class="user-dashboard-form-submit-enhanced">
                    <svg class="user-dashboard-product-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Cari
                </button>
            </div>
        </form>
    </div>

    <!-- Products Table -->
    <div class="user-dashboard-product-table-wrapper">
        @if($storeStock->count() > 0)
            <div class="user-dashboard-product-table-container">
                <table class="user-dashboard-product-table">
                    <thead class="user-dashboard-product-table-header">
                        <tr>
                            <th class="user-dashboard-product-table-th">Produk</th>
                            <th class="user-dashboard-product-table-th">Stok Saat Ini</th>
                            <th class="user-dashboard-product-table-th">Status</th>
                            <th class="user-dashboard-product-table-th">Aksi</th>
                        </tr>
                    </thead>
                    <tbody class="user-dashboard-product-table-body">
                        @foreach($storeStock as $stock)
                            <tr class="user-dashboard-product-table-row">
                                <td class="user-dashboard-product-table-td">
                                    <div class="user-dashboard-product-info">
                                        <div class="user-dashboard-product-name">{{ $stock->product->name }}</div>
                                        <div class="user-dashboard-product-meta">
                                            Terakhir diperbarui: {{ $stock->updated_at->format('d/m/Y H:i') }}
                                        </div>
                                    </div>
                                </td>
                                <td class="user-dashboard-product-table-td">
                                    <div class="user-dashboard-product-quantity">
                                        {{ number_format($stock->quantity) }} unit
                                    </div>
                                </td>
                                <td class="user-dashboard-product-table-td">
                                    @if($stock->quantity == 0)
                                        <span class="user-dashboard-product-status user-dashboard-product-status-danger">Habis</span>
                                    @elseif($stock->quantity <= 5)
                                        <span class="user-dashboard-product-status user-dashboard-product-status-warning">Stok Rendah</span>
                                    @else
                                        <span class="user-dashboard-product-status user-dashboard-product-status-success">Tersedia</span>
                                    @endif
                                </td>
                                <td class="user-dashboard-product-table-td">
                                    <div class="user-dashboard-product-actions">
                                        @if($stock->quantity > 0)
                                            <a href="{{ route('user.products.adjust.form', $stock->product) }}" 
                                               class="user-dashboard-product-btn user-dashboard-product-btn-primary">
                                                <svg class="user-dashboard-product-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                                </svg>
                                                Kelola
                                            </a>
                                            <form method="POST" action="{{ route('user.products.set-zero', $stock->product) }}"
                                                  class="user-dashboard-product-quick-form"
                                                  data-product-name="{{ $stock->product->name }}"
                                                  data-current-stock="{{ $stock->quantity }}">
                                                @csrf
                                                <button type="button"
                                                        class="user-dashboard-product-btn user-dashboard-product-btn-danger user-dashboard-set-zero-btn"
                                                        onclick="handleSetZeroClick(this)">
                                                    <svg class="user-dashboard-product-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                    </svg>
                                                    Habis
                                                </button>
                                            </form>
                                        @else
                                            <span class="user-dashboard-product-no-action">Stok habis</span>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="user-dashboard-product-pagination-wrapper">
                {{ $storeStock->appends(request()->query())->links('user.components.pagination') }}
            </div>
        @else
            <div class="user-dashboard-product-empty-state">
                <div class="user-dashboard-product-empty-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 class="user-dashboard-product-empty-title">Tidak Ada Produk</h3>
                <p class="user-dashboard-product-empty-description">
                    @if(request('search'))
                        Tidak ditemukan produk dengan kata kunci "{{ request('search') }}".
                    @else
                        Belum ada produk di stok toko Anda.
                    @endif
                </p>
                @if(request('search'))
                    <a href="{{ route('user.products.index') }}" class="user-dashboard-form-cancel-enhanced">
                        Lihat Semua Produk
                    </a>
                @endif
            </div>
        @endif
    </div>

    <!-- Set to Zero Confirmation Modal -->
    @include('user.components.confirmation-modal', [
        'modalId' => 'set-zero-modal',
        'title' => 'Konfirmasi Habis Stok',
        'type' => 'danger'
    ])
</div>

@push('scripts')
<script>
// Handle set zero button clicks with custom modal
function handleSetZeroClick(button) {
    const form = button.closest('form');
    const productName = form.dataset.productName;
    const currentStock = parseInt(form.dataset.currentStock);

    if (currentStock === 0) {
        alert('Stok produk ini sudah habis');
        return;
    }

    // Show set to zero confirmation modal
    UserModalManager.showSetToZeroConfirmation({
        productName: productName,
        currentStock: currentStock,
        onConfirm: function() {
            // Submit the form
            form.submit();
            return true;
        }
    });
}
</script>
<script src="{{ asset('js/user-dashboard-product-management.js') }}"></script>
@endpush
@endsection
