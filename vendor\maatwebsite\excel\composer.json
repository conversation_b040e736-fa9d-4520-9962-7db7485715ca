{"name": "maatwebsite/excel", "description": "An eloquent way of importing and exporting Excel and CSV in Laravel 4 with the power of PHPExcel", "license": "LGPL", "keywords": ["laravel", "phpexcel", "excel", "csv", "export", "import", "batch"], "authors": [{"name": "Maatwebsite.nl", "email": "<EMAIL>"}], "require": {"php": ">=5.3.0", "phpoffice/phpexcel": "~1.8.0"}, "require-dev": {"phpunit/phpunit": "~4.0", "mockery/mockery": "~0.9", "orchestra/testbench": "~2.2.0@dev"}, "autoload": {"classmap": ["src/Maatwebsite/Excel", "tests/TestCase.php"], "psr-0": {"Maatwebsite\\Excel\\": "src/"}}, "repositories": [{"type": "vcs", "url": "git://github.com/orchestral/phpseclib.git"}]}