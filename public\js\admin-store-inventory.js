/**
 * Enhanced Admin Store Inventory Management JavaScript
 * PT. Indah Berkah Abadi - Inventory System
 * Features: Smooth animations, real-time feedback, enhanced UX
 */

// Global variables
let currentStoreId = null;
let currentProductId = null;
let currentStock = 0;
let isSubmitting = false;

// Animation and UI configuration
const UI_CONFIG = {
    animationDuration: 300,
    notificationDuration: 5000,
    loadingDelay: 200,
    debounceDelay: 300
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeStoreInventory();
});

/**
 * Initialize store inventory functionality
 */
function initializeStoreInventory() {
    // Set up CSRF token for AJAX requests
    const token = document.querySelector('meta[name="csrf-token"]');
    if (token) {
        window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.getAttribute('content');
    }

    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('admin-dashboard-modal')) {
            closeAllModals();
        }
    });

    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
}



/**
 * Open stock adjustment modal
 */
function openStockAdjustModal(storeId, productId, productName, currentStockValue) {
    currentStoreId = storeId;
    currentProductId = productId;
    currentStock = parseInt(currentStockValue) || 0;

    // Set form values
    document.getElementById('adjust_store_id').value = storeId;
    document.getElementById('adjust_product_id').value = productId;
    document.getElementById('adjust_product_name').textContent = productName;
    document.getElementById('adjust_current_stock').textContent = currentStock + ' unit';

    // Reset form
    document.getElementById('stockAdjustForm').reset();
    document.getElementById('adjust_store_id').value = storeId;
    document.getElementById('adjust_product_id').value = productId;

    // Show modal
    document.getElementById('stockAdjustModal').classList.add('active');
}

/**
 * Close stock adjustment modal
 */
function closeStockAdjustModal() {
    document.getElementById('stockAdjustModal').classList.remove('active');
    currentStoreId = null;
    currentProductId = null;
    currentStock = 0;
    isSubmitting = false;
}

/**
 * Submit stock adjustment
 */
function submitStockAdjustment() {
    const form = document.getElementById('stockAdjustForm');
    const formData = new FormData(form);

    // Validate form
    const adjustmentType = formData.get('adjustment_type');
    const quantity = parseInt(formData.get('quantity'));

    if (!adjustmentType) {
        showNotification('Pilih jenis penyesuaian', 'error');
        return;
    }

    if (!quantity || quantity < 0) {
        showNotification('Masukkan jumlah yang valid', 'error');
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('#stockAdjustModal .admin-dashboard-btn-primary');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Memproses...';
    submitBtn.disabled = true;

    // Submit via AJAX
    fetch('/admin/store-inventory/adjust', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            closeStockAdjustModal();
            // Reload page to show updated data
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            if (data.errors) {
                // Handle validation errors
                let errorMessage = 'Validasi gagal:\n';
                for (const [field, messages] of Object.entries(data.errors)) {
                    errorMessage += `- ${messages.join(', ')}\n`;
                }
                showNotification(errorMessage, 'error');
            } else {
                showNotification(data.message || 'Terjadi kesalahan', 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Terjadi kesalahan saat memproses permintaan', 'error');
    })
    .finally(() => {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}





/**
 * View distribution history - simplified version
 */
function viewDistributionHistory(storeId, productId, productName) {
    // Set modal title
    document.querySelector('#distributionHistoryModal .admin-dashboard-modal-title').textContent =
        `Riwayat Distribusi - ${productName}`;

    // Show loading
    const content = document.getElementById('distributionHistoryContent');
    content.innerHTML = `
        <div class="admin-dashboard-loading">
            <div class="admin-dashboard-loading-spinner"></div>
            <p>Memuat riwayat distribusi...</p>
        </div>
    `;

    // Show modal
    document.getElementById('distributionHistoryModal').classList.add('active');

    // Load distribution history
    fetch(`/admin/store-inventory/distribution-history?store_id=${storeId}&product_id=${productId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderDistributionHistory(data.data);
        } else {
            content.innerHTML = `
                <div class="admin-dashboard-error-state">
                    <p>Gagal memuat riwayat distribusi</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        content.innerHTML = `
            <div class="admin-dashboard-error-state">
                <p>Terjadi kesalahan saat memuat data</p>
            </div>
        `;
    });
}

/**
 * Render distribution history - simplified version
 */
function renderDistributionHistory(distributions) {
    const content = document.getElementById('distributionHistoryContent');

    if (distributions.length === 0) {
        content.innerHTML = `
            <div class="admin-dashboard-empty-state">
                <svg class="admin-dashboard-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
                <h3 class="admin-dashboard-empty-title">Belum Ada Riwayat</h3>
                <p class="admin-dashboard-empty-description">Produk ini belum pernah didistribusikan ke toko ini.</p>
            </div>
        `;
        return;
    }

    let html = `
        <div class="admin-dashboard-table-container">
            <table class="admin-dashboard-table">
                <thead class="admin-dashboard-table-header">
                    <tr>
                        <th class="admin-dashboard-table-th">Tanggal</th>
                        <th class="admin-dashboard-table-th">Jumlah Kirim</th>
                        <th class="admin-dashboard-table-th">Jumlah Terima</th>
                        <th class="admin-dashboard-table-th">Status</th>
                    </tr>
                </thead>
                <tbody class="admin-dashboard-table-body">
    `;

    distributions.forEach(dist => {
        const statusClass = dist.confirmed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
        const statusText = dist.confirmed ? 'Dikonfirmasi' : 'Belum Dikonfirmasi';

        html += `
            <tr class="admin-dashboard-table-row">
                <td class="admin-dashboard-table-td">${formatDate(dist.date_distributed)}</td>
                <td class="admin-dashboard-table-td">${dist.quantity}</td>
                <td class="admin-dashboard-table-td">${dist.received_quantity || '-'}</td>
                <td class="admin-dashboard-table-td">
                    <span class="admin-dashboard-badge ${statusClass}">${statusText}</span>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    content.innerHTML = html;
}



/**
 * Close distribution history modal
 */
function closeDistributionHistoryModal() {
    document.getElementById('distributionHistoryModal').classList.remove('active');
}

/**
 * Close all modals
 */
function closeAllModals() {
    closeStockAdjustModal();
    closeDistributionHistoryModal();
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.admin-dashboard-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `admin-dashboard-notification admin-dashboard-notification-${type}`;

    const iconSvg = type === 'success'
        ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>'
        : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';

    notification.innerHTML = `
        <div class="admin-dashboard-notification-content">
            <svg class="admin-dashboard-notification-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                ${iconSvg}
            </svg>
            <span>${message}</span>
        </div>
        <button class="admin-dashboard-notification-close" onclick="this.parentElement.remove()">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}



/**
 * Get default icon for notification type
 */
function getDefaultIcon(type) {
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    return icons[type] || icons.info;
}

/**
 * Get icon SVG for notification type
 */
function getIconSvg(type) {
    const icons = {
        success: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>',
        error: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>',
        warning: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>',
        info: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>'
    };
    return icons[type] || icons.info;
}

/**
 * Get type label for notification
 */
function getTypeLabel(type) {
    const labels = {
        success: 'Berhasil',
        error: 'Error',
        warning: 'Peringatan',
        info: 'Informasi'
    };
    return labels[type] || labels.info;
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    };
    return date.toLocaleDateString('id-ID', options);
}

/**
 * Handle form submission with Enter key
 */
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.target.closest('#stockAdjustForm')) {
        e.preventDefault();
        submitStockAdjustment();
    }
});








