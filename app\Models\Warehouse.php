<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Warehouse extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'location',
        'code',
        'description',
        'status',
    ];

    protected $casts = [
        'status' => 'string',
    ];

    /**
     * Get the users (admins) assigned to this warehouse.
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the warehouse stock for this warehouse.
     */
    public function warehouseStock()
    {
        return $this->hasMany(WarehouseStock::class);
    }

    /**
     * Get the distributions from this warehouse.
     */
    public function distributions()
    {
        return $this->hasMany(Distribution::class);
    }

    /**
     * Get the supplier deliveries to this warehouse.
     */
    public function supplierDeliveries()
    {
        return $this->hasMany(SupplierDelivery::class);
    }

    /**
     * Get the returns from this warehouse.
     */
    public function returns()
    {
        return $this->hasMany(ReturnModel::class);
    }

    /**
     * Get the stock movements for this warehouse.
     */
    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * Scope a query to only include active warehouses.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive warehouses.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Get total stock quantity for this warehouse.
     */
    public function getTotalStockQuantity()
    {
        return $this->warehouseStock()->sum('quantity');
    }

    /**
     * Get total number of products in this warehouse.
     */
    public function getTotalProductsCount()
    {
        return $this->warehouseStock()->distinct('product_id')->count('product_id');
    }

    /**
     * Get total distributions from this warehouse.
     */
    public function getTotalDistributionsCount()
    {
        return $this->distributions()->count();
    }

    /**
     * Get total supplier deliveries to this warehouse.
     */
    public function getTotalDeliveriesCount()
    {
        return $this->supplierDeliveries()->count();
    }

    /**
     * Get total returns from this warehouse.
     */
    public function getTotalReturnsCount()
    {
        return $this->returns()->count();
    }
}
