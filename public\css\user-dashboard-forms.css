/* 
 * Independent CSS for User Dashboard Form Interactions
 * PT. Indah Berkah Abadi - Inventory System
 * Prevents conflicts with other CSS frameworks and ensures proper z-index hierarchy
 * 
 * Z-INDEX HIERARCHY:
 * - Sidebar navigation: 1000+ (highest priority)
 * - Form elements (focused): 50 (medium priority, below sidebar)
 * - Form elements (normal): 10 (low priority)
 * - Form containers: 5 (container level)
 * - Main content: 1 (lowest priority)
 */

/* ===== FORM CONTAINER ENHANCEMENTS ===== */

/* Enhanced form containers */
.user-dashboard-form-container-enhanced {
    position: relative !important;
    z-index: 5 !important;
    isolation: isolate !important;
    background-color: #ffffff !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    box-sizing: border-box !important;
}

/* Enhanced form groups */
.user-dashboard-form-group-enhanced {
    position: relative !important;
    z-index: 10 !important;
    margin-bottom: 1.5rem !important;
    isolation: isolate !important;
}

/* Enhanced filter groups */
.user-dashboard-filter-group-enhanced {
    position: relative !important;
    z-index: 10 !important;
    margin-bottom: 1rem !important;
    isolation: isolate !important;
}

/* ===== INPUT FIELD ENHANCEMENTS ===== */

/* Enhanced text inputs */
.user-dashboard-form-input-enhanced {
    position: relative !important;
    z-index: 10 !important;
    display: block !important;
    width: 100% !important;
    min-height: 44px !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    color: #111827 !important;
    background-color: #ffffff !important;
    border: 2px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    box-sizing: border-box !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    touch-action: manipulation !important;
}

/* Enhanced number inputs */
.user-dashboard-form-number-enhanced {
    position: relative !important;
    z-index: 10 !important;
    display: block !important;
    width: 100% !important;
    min-height: 44px !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    color: #111827 !important;
    background-color: #ffffff !important;
    border: 2px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    box-sizing: border-box !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    touch-action: manipulation !important;
    -moz-appearance: textfield !important;
}

/* Enhanced password inputs */
.user-dashboard-form-password-enhanced {
    position: relative !important;
    z-index: 10 !important;
    display: block !important;
    width: 100% !important;
    min-height: 44px !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    color: #111827 !important;
    background-color: #ffffff !important;
    border: 2px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    box-sizing: border-box !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    touch-action: manipulation !important;
}

/* Enhanced date inputs */
.user-dashboard-form-date-enhanced {
    position: relative !important;
    z-index: 10 !important;
    display: block !important;
    width: 100% !important;
    min-height: 44px !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    color: #111827 !important;
    background-color: #ffffff !important;
    border: 2px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    box-sizing: border-box !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    touch-action: manipulation !important;
}

/* Enhanced textarea */
.user-dashboard-form-textarea-enhanced {
    position: relative !important;
    z-index: 10 !important;
    display: block !important;
    width: 100% !important;
    min-height: 88px !important;
    padding: 0.75rem !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    color: #111827 !important;
    background-color: #ffffff !important;
    border: 2px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    box-sizing: border-box !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    touch-action: manipulation !important;
    resize: vertical !important;
}

/* ===== FOCUS STATES ===== */

/* Input focus states */
.user-dashboard-form-input-enhanced:focus,
.user-dashboard-form-number-enhanced:focus,
.user-dashboard-form-password-enhanced:focus,
.user-dashboard-form-date-enhanced:focus,
.user-dashboard-form-textarea-enhanced:focus {
    border-color: #166534 !important;
    box-shadow: 0 0 0 3px rgba(22, 101, 52, 0.1) !important;
    outline: 2px solid transparent !important;
    outline-offset: 2px !important;
    z-index: 50 !important;
}

/* Hover states */
.user-dashboard-form-input-enhanced:hover,
.user-dashboard-form-number-enhanced:hover,
.user-dashboard-form-password-enhanced:hover,
.user-dashboard-form-date-enhanced:hover,
.user-dashboard-form-textarea-enhanced:hover {
    border-color: #9ca3af !important;
}

/* ===== BUTTON ENHANCEMENTS ===== */

/* Enhanced submit buttons */
.user-dashboard-form-submit-enhanced {
    position: relative !important;
    z-index: 10 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    line-height: 1.5 !important;
    color: #ffffff !important;
    background: linear-gradient(135deg, #166534 0%, #15803d 100%) !important;
    border: 2px solid #166534 !important;
    border-radius: 0.5rem !important;
    cursor: pointer !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    touch-action: manipulation !important;
    box-sizing: border-box !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    text-decoration: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
}

/* Enhanced cancel/secondary buttons */
.user-dashboard-form-cancel-enhanced {
    position: relative !important;
    z-index: 10 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    line-height: 1.5 !important;
    color: #374151 !important;
    background-color: #ffffff !important;
    border: 2px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    cursor: pointer !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    touch-action: manipulation !important;
    box-sizing: border-box !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    text-decoration: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
}

/* Button focus states */
.user-dashboard-form-submit-enhanced:focus,
.user-dashboard-form-cancel-enhanced:focus {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 2px !important;
    z-index: 50 !important;
}

/* Button hover states */
.user-dashboard-form-submit-enhanced:hover {
    background: linear-gradient(135deg, #15803d 0%, #166534 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(22, 101, 52, 0.2) !important;
}

.user-dashboard-form-cancel-enhanced:hover {
    background-color: #f9fafb !important;
    border-color: #9ca3af !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* ===== MOBILE RESPONSIVE ENHANCEMENTS ===== */

@media (max-width: 768px) {
    /* Mobile form container improvements */
    .user-dashboard-form-container-enhanced {
        padding: 0.75rem !important;
        z-index: 5 !important;
    }

    /* Mobile form group improvements */
    .user-dashboard-form-group-enhanced,
    .user-dashboard-filter-group-enhanced {
        margin-bottom: 1rem !important;
        z-index: 10 !important;
    }

    /* Mobile input improvements */
    .user-dashboard-form-input-enhanced,
    .user-dashboard-form-number-enhanced,
    .user-dashboard-form-password-enhanced,
    .user-dashboard-form-date-enhanced,
    .user-dashboard-form-textarea-enhanced {
        min-height: 44px !important;
        padding: 0.75rem !important;
        font-size: 1rem !important;
        z-index: 10 !important;
    }

    /* Mobile button improvements */
    .user-dashboard-form-submit-enhanced,
    .user-dashboard-form-cancel-enhanced {
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 0.75rem 1rem !important;
        font-size: 1rem !important;
        z-index: 10 !important;
    }

    /* Enhanced focus states for mobile */
    .user-dashboard-form-input-enhanced:focus,
    .user-dashboard-form-number-enhanced:focus,
    .user-dashboard-form-password-enhanced:focus,
    .user-dashboard-form-date-enhanced:focus,
    .user-dashboard-form-textarea-enhanced:focus,
    .user-dashboard-form-submit-enhanced:focus,
    .user-dashboard-form-cancel-enhanced:focus {
        z-index: 50 !important;
        outline: 2px solid #3b82f6 !important;
        outline-offset: 2px !important;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .user-dashboard-form-input-enhanced,
    .user-dashboard-form-number-enhanced,
    .user-dashboard-form-password-enhanced,
    .user-dashboard-form-date-enhanced,
    .user-dashboard-form-textarea-enhanced {
        padding: 0.625rem !important;
        font-size: 0.875rem !important;
    }

    .user-dashboard-form-submit-enhanced,
    .user-dashboard-form-cancel-enhanced {
        padding: 0.625rem 0.875rem !important;
        font-size: 0.875rem !important;
    }
}

/* ===== FORM LABEL ENHANCEMENTS ===== */

.user-dashboard-form-label-enhanced {
    display: block !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.5 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* ===== ERROR STATE ENHANCEMENTS ===== */

.user-dashboard-form-error-enhanced {
    color: #dc2626 !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
    line-height: 1.4 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.user-dashboard-form-input-error {
    border-color: #dc2626 !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

/* ===== SUCCESS STATE ENHANCEMENTS ===== */

.user-dashboard-form-success-enhanced {
    color: #059669 !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
    line-height: 1.4 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.user-dashboard-form-input-success {
    border-color: #059669 !important;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1) !important;
}

/* ===== DISABLED STATE ENHANCEMENTS ===== */

.user-dashboard-form-input-enhanced:disabled,
.user-dashboard-form-number-enhanced:disabled,
.user-dashboard-form-password-enhanced:disabled,
.user-dashboard-form-date-enhanced:disabled,
.user-dashboard-form-textarea-enhanced:disabled {
    background-color: #f9fafb !important;
    color: #6b7280 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

.user-dashboard-form-submit-enhanced:disabled,
.user-dashboard-form-cancel-enhanced:disabled {
    background-color: #f9fafb !important;
    color: #6b7280 !important;
    border-color: #d1d5db !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
    .user-dashboard-form-input-enhanced,
    .user-dashboard-form-number-enhanced,
    .user-dashboard-form-password-enhanced,
    .user-dashboard-form-date-enhanced,
    .user-dashboard-form-textarea-enhanced {
        border-width: 3px !important;
        border-color: #000000 !important;
    }

    .user-dashboard-form-input-enhanced:focus,
    .user-dashboard-form-number-enhanced:focus,
    .user-dashboard-form-password-enhanced:focus,
    .user-dashboard-form-date-enhanced:focus,
    .user-dashboard-form-textarea-enhanced:focus {
        border-color: #0066cc !important;
        box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.3) !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .user-dashboard-form-input-enhanced,
    .user-dashboard-form-number-enhanced,
    .user-dashboard-form-password-enhanced,
    .user-dashboard-form-date-enhanced,
    .user-dashboard-form-textarea-enhanced,
    .user-dashboard-form-submit-enhanced,
    .user-dashboard-form-cancel-enhanced {
        transition: none !important;
    }
}

/* ===== BROWSER-SPECIFIC FIXES ===== */

/* Firefox number input fixes */
.user-dashboard-form-number-enhanced::-moz-number-spin-box {
    -moz-appearance: textfield !important;
}

/* Webkit number input fixes */
.user-dashboard-form-number-enhanced::-webkit-outer-spin-button,
.user-dashboard-form-number-enhanced::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
}

/* Safari date input fixes */
.user-dashboard-form-date-enhanced::-webkit-calendar-picker-indicator {
    cursor: pointer !important;
    filter: invert(0.5) !important;
}

/* ===== FORM BUTTON GROUP ENHANCEMENTS ===== */

.user-dashboard-form-button-group-enhanced {
    display: flex !important;
    gap: 0.75rem !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex-wrap: wrap !important;
    margin-top: 1.5rem !important;
    position: relative !important;
    z-index: 10 !important;
}

@media (max-width: 640px) {
    .user-dashboard-form-button-group-enhanced {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 0.5rem !important;
    }

    .user-dashboard-form-submit-enhanced,
    .user-dashboard-form-cancel-enhanced {
        width: 100% !important;
    }
}

/* ===== DEBUGGING HELPERS ===== */

/* Debug mode - uncomment to visualize z-index layers */
/*
.user-dashboard-form-container-enhanced {
    box-shadow: 0 0 0 2px rgba(0, 255, 0, 0.3) !important;
}

.user-dashboard-form-group-enhanced {
    background-color: rgba(255, 255, 0, 0.1) !important;
}

.user-dashboard-form-input-enhanced,
.user-dashboard-form-number-enhanced,
.user-dashboard-form-password-enhanced,
.user-dashboard-form-date-enhanced,
.user-dashboard-form-textarea-enhanced {
    box-shadow: 0 0 0 1px rgba(255, 0, 0, 0.3) !important;
}
*/
