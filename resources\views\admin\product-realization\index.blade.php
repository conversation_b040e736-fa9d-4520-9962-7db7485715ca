@extends('layouts.admin')

@section('title', 'Realisasi Produk - Admin Dashboard')
@section('page-title', 'Realisasi Produk')

@section('content')
<div class="space-y-6">
        <!-- Header -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-content">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Realisasi Produk</h1>
                        <p class="text-gray-600 mt-1">Pantau perbedaan antara jumlah distribusi yang direncanakan dengan yang diterima</p>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="{{ route('admin.product-realization.export', request()->query()) }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                            </svg>
                            Export Data
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Time Period Filter -->
        @include('admin.components.time-period-filter')

        <!-- Statistics Cards - Compact Version -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-content">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ number_format($stats['total_distributions']) }}</div>
                            <div class="text-xs text-gray-600">Total Distribusi</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ number_format($stats['total_planned']) }}</div>
                            <div class="text-xs text-gray-600">Jumlah Direncanakan</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ number_format($stats['total_received']) }}</div>
                            <div class="text-xs text-gray-600">Jumlah Diterima</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ $stats['realization_percentage'] }}%</div>
                            <div class="text-xs text-gray-600">Tingkat Realisasi</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-content">
                <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-700">Filter Tambahan</span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">Gunakan filter di bawah untuk mempersempit pencarian dalam periode waktu yang dipilih</p>
                </div>
                <form method="GET" action="{{ route('admin.product-realization.index') }}">
                    <!-- Preserve time period parameter -->
                    @if(request('period'))
                        <input type="hidden" name="period" value="{{ request('period') }}">
                    @endif
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Toko</label>
                            <select name="store" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Semua Toko</option>
                                @foreach($stores as $store)
                                    <option value="{{ $store->id }}" {{ $storeFilter == $store->id ? 'selected' : '' }}>
                                        {{ $store->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Produk</label>
                            <select name="product" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Semua Produk</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}" {{ $productFilter == $product->id ? 'selected' : '' }}>
                                        {{ $product->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Dari Tanggal</label>
                            <input type="date" name="date_from" value="{{ $dateFrom }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Sampai Tanggal</label>
                            <input type="date" name="date_to" value="{{ $dateTo }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary w-full">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Filter
                            </button>
                        </div>
                        <div class="flex items-end">
                            <a href="{{ route('admin.product-realization.index') }}" class="admin-dashboard-btn admin-dashboard-btn-secondary w-full text-center">
                                Reset
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Product Realization Table -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Data Realisasi Produk</h2>
            </div>
            <div class="admin-dashboard-card-content">
                @if($distributions->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3">Produk</th>
                                    <th class="px-6 py-3">Toko</th>
                                    <th class="px-6 py-3">Tanggal Distribusi</th>
                                    <th class="px-6 py-3">Jumlah Direncanakan</th>
                                    <th class="px-6 py-3">Jumlah Diterima</th>
                                    <th class="px-6 py-3">Selisih</th>
                                    <th class="px-6 py-3">Catatan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($distributions as $distribution)
                                <tr class="bg-white border-b hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="font-medium text-gray-900">{{ $distribution->product->name }}</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="font-medium text-gray-900">{{ $distribution->store->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $distribution->store->location }}</div>
                                    </td>
                                    <td class="px-6 py-4">{{ $distribution->date_distributed->format('d/m/Y') }}</td>
                                    <td class="px-6 py-4">
                                        <span class="font-medium text-gray-900">{{ number_format($distribution->quantity) }}</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="font-medium text-gray-900">{{ number_format($distribution->received_quantity) }}</span>
                                    </td>
                                    <td class="px-6 py-4">
                                        @php
                                            $difference = $distribution->received_quantity - $distribution->quantity;
                                        @endphp
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            @if($difference == 0) bg-green-100 text-green-800
                                            @elseif($difference > 0) bg-blue-100 text-blue-800
                                            @else bg-red-100 text-red-800
                                            @endif">
                                            @if($difference == 0)
                                                Sesuai
                                            @elseif($difference > 0)
                                                +{{ number_format($difference) }}
                                            @else
                                                {{ number_format($difference) }}
                                            @endif
                                        </span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-600">
                                            {{ $distribution->notes ?: '-' }}
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="text-gray-500 mb-2">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada data realisasi</h3>
                        <p class="text-gray-500">Belum ada data realisasi produk yang ditemukan untuk periode ini.</p>
                    </div>
                @endif

                <!-- Pagination -->
                @if($distributions->hasPages())
                <div class="mt-4">
                    {{ $distributions->appends(request()->query())->links('pagination.admin-dashboard') }}
                </div>
                @endif
            </div>
        </div>
</div>
@endsection
