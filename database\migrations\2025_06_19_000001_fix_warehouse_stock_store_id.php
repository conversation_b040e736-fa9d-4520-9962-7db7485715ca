<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix existing warehouse stock entries to have store_id = null for central warehouse
        // This ensures proper stock calculation for the central warehouse
        
        // First, let's check if there's a "Gudang Pusat" store
        $centralWarehouseStore = DB::table('stores')->where('name', 'Gudang Pusat')->first();
        
        if ($centralWarehouseStore) {
            // Update warehouse stock entries that belong to "Gudang Pusat" to have store_id = null
            DB::table('warehouse_stock')
                ->where('store_id', $centralWarehouseStore->id)
                ->update(['store_id' => null]);
                
            echo "Updated warehouse stock entries for central warehouse (Gudang Pusat) to have store_id = null\n";
        }
        
        // Also update any warehouse stock entries that might have other store_ids to null
        // since warehouse stock should only be for central warehouse
        DB::table('warehouse_stock')
            ->whereNotNull('store_id')
            ->update(['store_id' => null]);
            
        echo "All warehouse stock entries now have store_id = null (central warehouse)\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not reversible as we're fixing data integrity
        // The original data structure was incorrect
    }
};
