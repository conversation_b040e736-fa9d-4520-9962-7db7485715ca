function De(e,t){return function(){return e.apply(t,arguments)}}const{toString:at}=Object.prototype,{getPrototypeOf:de}=Object,{iterator:X,toStringTag:Pe}=Symbol,G=(e=>t=>{const n=at.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),L=e=>(e=e.toLowerCase(),t=>G(t)===e),Z=e=>t=>typeof t===e,{isArray:U}=Array,j=Z("undefined");function ct(e){return e!==null&&!j(e)&&e.constructor!==null&&!j(e.constructor)&&O(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const qe=L("ArrayBuffer");function lt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&qe(e.buffer),t}const ut=Z("string"),O=Z("function"),_e=Z("number"),Q=e=>e!==null&&typeof e=="object",dt=e=>e===!0||e===!1,H=e=>{if(G(e)!=="object")return!1;const t=de(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Pe in e)&&!(X in e)},ft=L("Date"),pt=L("File"),mt=L("Blob"),ht=L("FileList"),yt=e=>Q(e)&&O(e.pipe),bt=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||O(e.append)&&((t=G(e))==="formdata"||t==="object"&&O(e.toString)&&e.toString()==="[object FormData]"))},wt=L("URLSearchParams"),[gt,Et,St,Rt]=["ReadableStream","Request","Response","Headers"].map(L),Tt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function I(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),U(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function Ue(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const D=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Be=e=>!j(e)&&e!==D;function oe(){const{caseless:e}=Be(this)&&this||{},t={},n=(r,s)=>{const o=e&&Ue(t,s)||s;H(t[o])&&H(r)?t[o]=oe(t[o],r):H(r)?t[o]=oe({},r):U(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&I(arguments[r],n);return t}const At=(e,t,n,{allOwnKeys:r}={})=>(I(t,(s,o)=>{n&&O(s)?e[o]=De(s,n):e[o]=s},{allOwnKeys:r}),e),Ot=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),xt=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Lt=(e,t,n,r)=>{let s,o,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&de(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Ct=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Nt=e=>{if(!e)return null;if(U(e))return e;let t=e.length;if(!_e(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Ft=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&de(Uint8Array)),kt=(e,t)=>{const r=(e&&e[X]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Dt=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Pt=L("HTMLFormElement"),qt=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),ge=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),_t=L("RegExp"),ve=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};I(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},Ut=e=>{ve(e,(t,n)=>{if(O(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(O(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Bt=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return U(e)?r(e):r(String(e).split(t)),n},vt=()=>{},jt=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function It(e){return!!(e&&O(e.append)&&e[Pe]==="FormData"&&e[X])}const Mt=e=>{const t=new Array(10),n=(r,s)=>{if(Q(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=U(r)?[]:{};return I(r,(i,c)=>{const d=n(i,s+1);!j(d)&&(o[c]=d)}),t[s]=void 0,o}}return r};return n(e,0)},zt=L("AsyncFunction"),Ht=e=>e&&(Q(e)||O(e))&&O(e.then)&&O(e.catch),je=((e,t)=>e?setImmediate:t?((n,r)=>(D.addEventListener("message",({source:s,data:o})=>{s===D&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),D.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",O(D.postMessage)),$t=typeof queueMicrotask<"u"?queueMicrotask.bind(D):typeof process<"u"&&process.nextTick||je,Jt=e=>e!=null&&O(e[X]),a={isArray:U,isArrayBuffer:qe,isBuffer:ct,isFormData:bt,isArrayBufferView:lt,isString:ut,isNumber:_e,isBoolean:dt,isObject:Q,isPlainObject:H,isReadableStream:gt,isRequest:Et,isResponse:St,isHeaders:Rt,isUndefined:j,isDate:ft,isFile:pt,isBlob:mt,isRegExp:_t,isFunction:O,isStream:yt,isURLSearchParams:wt,isTypedArray:Ft,isFileList:ht,forEach:I,merge:oe,extend:At,trim:Tt,stripBOM:Ot,inherits:xt,toFlatObject:Lt,kindOf:G,kindOfTest:L,endsWith:Ct,toArray:Nt,forEachEntry:kt,matchAll:Dt,isHTMLForm:Pt,hasOwnProperty:ge,hasOwnProp:ge,reduceDescriptors:ve,freezeMethods:Ut,toObjectSet:Bt,toCamelCase:qt,noop:vt,toFiniteNumber:jt,findKey:Ue,global:D,isContextDefined:Be,isSpecCompliantForm:It,toJSONObject:Mt,isAsyncFn:zt,isThenable:Ht,setImmediate:je,asap:$t,isIterable:Jt};function h(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(h,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Ie=h.prototype,Me={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Me[e]={value:e}});Object.defineProperties(h,Me);Object.defineProperty(Ie,"isAxiosError",{value:!0});h.from=(e,t,n,r,s,o)=>{const i=Object.create(Ie);return a.toFlatObject(e,i,function(d){return d!==Error.prototype},c=>c!=="isAxiosError"),h.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Vt=null;function ie(e){return a.isPlainObject(e)||a.isArray(e)}function ze(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function Ee(e,t,n){return e?e.concat(t).map(function(s,o){return s=ze(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function Wt(e){return a.isArray(e)&&!e.some(ie)}const Kt=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function Y(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,m){return!a.isUndefined(m[y])});const r=n.metaTokens,s=n.visitor||u,o=n.dots,i=n.indexes,d=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function l(f){if(f===null)return"";if(a.isDate(f))return f.toISOString();if(a.isBoolean(f))return f.toString();if(!d&&a.isBlob(f))throw new h("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(f)||a.isTypedArray(f)?d&&typeof Blob=="function"?new Blob([f]):Buffer.from(f):f}function u(f,y,m){let w=f;if(f&&!m&&typeof f=="object"){if(a.endsWith(y,"{}"))y=r?y:y.slice(0,-2),f=JSON.stringify(f);else if(a.isArray(f)&&Wt(f)||(a.isFileList(f)||a.endsWith(y,"[]"))&&(w=a.toArray(f)))return y=ze(y),w.forEach(function(R,N){!(a.isUndefined(R)||R===null)&&t.append(i===!0?Ee([y],N,o):i===null?y:y+"[]",l(R))}),!1}return ie(f)?!0:(t.append(Ee(m,y,o),l(f)),!1)}const p=[],b=Object.assign(Kt,{defaultVisitor:u,convertValue:l,isVisitable:ie});function E(f,y){if(!a.isUndefined(f)){if(p.indexOf(f)!==-1)throw Error("Circular reference detected in "+y.join("."));p.push(f),a.forEach(f,function(w,S){(!(a.isUndefined(w)||w===null)&&s.call(t,w,a.isString(S)?S.trim():S,y,b))===!0&&E(w,y?y.concat(S):[S])}),p.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return E(e),t}function Se(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function fe(e,t){this._pairs=[],e&&Y(e,this,t)}const He=fe.prototype;He.append=function(t,n){this._pairs.push([t,n])};He.toString=function(t){const n=t?function(r){return t.call(this,r,Se)}:Se;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function Xt(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $e(e,t,n){if(!t)return e;const r=n&&n.encode||Xt;a.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=a.isURLSearchParams(t)?t.toString():new fe(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Re{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Je={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Gt=typeof URLSearchParams<"u"?URLSearchParams:fe,Zt=typeof FormData<"u"?FormData:null,Qt=typeof Blob<"u"?Blob:null,Yt={isBrowser:!0,classes:{URLSearchParams:Gt,FormData:Zt,Blob:Qt},protocols:["http","https","file","blob","url","data"]},pe=typeof window<"u"&&typeof document<"u",ae=typeof navigator=="object"&&navigator||void 0,en=pe&&(!ae||["ReactNative","NativeScript","NS"].indexOf(ae.product)<0),tn=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",nn=pe&&window.location.href||"http://localhost",rn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:pe,hasStandardBrowserEnv:en,hasStandardBrowserWebWorkerEnv:tn,navigator:ae,origin:nn},Symbol.toStringTag,{value:"Module"})),T={...rn,...Yt};function sn(e,t){return Y(e,new T.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return T.isNode&&a.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function on(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function an(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function Ve(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),d=o>=n.length;return i=!i&&a.isArray(s)?s.length:i,d?(a.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!c):((!s[i]||!a.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&a.isArray(s[i])&&(s[i]=an(s[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(r,s)=>{t(on(r),s,n,0)}),n}return null}function cn(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const M={transitional:Je,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=a.isObject(t);if(o&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return s?JSON.stringify(Ve(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return sn(t,this.formSerializer).toString();if((c=a.isFileList(t))||r.indexOf("multipart/form-data")>-1){const d=this.env&&this.env.FormData;return Y(c?{"files[]":t}:t,d&&new d,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),cn(t)):t}],transformResponse:[function(t){const n=this.transitional||M.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?h.from(c,h.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:T.classes.FormData,Blob:T.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{M.headers[e]={}});const ln=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),un=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&ln[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Te=Symbol("internals");function v(e){return e&&String(e).trim().toLowerCase()}function $(e){return e===!1||e==null?e:a.isArray(e)?e.map($):String(e)}function dn(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const fn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ne(e,t,n,r,s){if(a.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!a.isString(t)){if(a.isString(r))return t.indexOf(r)!==-1;if(a.isRegExp(r))return r.test(t)}}function pn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function mn(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let x=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(c,d,l){const u=v(d);if(!u)throw new Error("header name must be a non-empty string");const p=a.findKey(s,u);(!p||s[p]===void 0||l===!0||l===void 0&&s[p]!==!1)&&(s[p||d]=$(c))}const i=(c,d)=>a.forEach(c,(l,u)=>o(l,u,d));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!fn(t))i(un(t),n);else if(a.isObject(t)&&a.isIterable(t)){let c={},d,l;for(const u of t){if(!a.isArray(u))throw TypeError("Object iterator must return a key-value pair");c[l=u[0]]=(d=c[l])?a.isArray(d)?[...d,u[1]]:[d,u[1]]:u[1]}i(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=v(t),t){const r=a.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return dn(s);if(a.isFunction(n))return n.call(this,s,r);if(a.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=v(t),t){const r=a.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ne(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=v(i),i){const c=a.findKey(r,i);c&&(!n||ne(r,r[c],c,n))&&(delete r[c],s=!0)}}return a.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||ne(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return a.forEach(this,(s,o)=>{const i=a.findKey(r,o);if(i){n[i]=$(s),delete n[o];return}const c=t?pn(o):String(o).trim();c!==o&&delete n[o],n[c]=$(s),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&a.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Te]=this[Te]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=v(i);r[c]||(mn(s,i),r[c]=!0)}return a.isArray(t)?t.forEach(o):o(t),this}};x.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(x.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});a.freezeMethods(x);function re(e,t){const n=this||M,r=t||n,s=x.from(r.headers);let o=r.data;return a.forEach(e,function(c){o=c.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function We(e){return!!(e&&e.__CANCEL__)}function B(e,t,n){h.call(this,e??"canceled",h.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(B,h,{__CANCEL__:!0});function Ke(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new h("Request failed with status code "+n.status,[h.ERR_BAD_REQUEST,h.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function hn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function yn(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(d){const l=Date.now(),u=r[o];i||(i=l),n[s]=d,r[s]=l;let p=o,b=0;for(;p!==s;)b+=n[p++],p=p%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),l-i<t)return;const E=u&&l-u;return E?Math.round(b*1e3/E):void 0}}function bn(e,t){let n=0,r=1e3/t,s,o;const i=(l,u=Date.now())=>{n=u,s=null,o&&(clearTimeout(o),o=null),e.apply(null,l)};return[(...l)=>{const u=Date.now(),p=u-n;p>=r?i(l,u):(s=l,o||(o=setTimeout(()=>{o=null,i(s)},r-p)))},()=>s&&i(s)]}const V=(e,t,n=3)=>{let r=0;const s=yn(50,250);return bn(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,d=i-r,l=s(d),u=i<=c;r=i;const p={loaded:i,total:c,progress:c?i/c:void 0,bytes:d,rate:l||void 0,estimated:l&&c&&u?(c-i)/l:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(p)},n)},Ae=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Oe=e=>(...t)=>a.asap(()=>e(...t)),wn=T.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,T.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(T.origin),T.navigator&&/(msie|trident)/i.test(T.navigator.userAgent)):()=>!0,gn=T.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(r)&&i.push("path="+r),a.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function En(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Sn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Xe(e,t,n){let r=!En(t);return e&&(r||n==!1)?Sn(e,t):t}const xe=e=>e instanceof x?{...e}:e;function q(e,t){t=t||{};const n={};function r(l,u,p,b){return a.isPlainObject(l)&&a.isPlainObject(u)?a.merge.call({caseless:b},l,u):a.isPlainObject(u)?a.merge({},u):a.isArray(u)?u.slice():u}function s(l,u,p,b){if(a.isUndefined(u)){if(!a.isUndefined(l))return r(void 0,l,p,b)}else return r(l,u,p,b)}function o(l,u){if(!a.isUndefined(u))return r(void 0,u)}function i(l,u){if(a.isUndefined(u)){if(!a.isUndefined(l))return r(void 0,l)}else return r(void 0,u)}function c(l,u,p){if(p in t)return r(l,u);if(p in e)return r(void 0,l)}const d={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(l,u,p)=>s(xe(l),xe(u),p,!0)};return a.forEach(Object.keys(Object.assign({},e,t)),function(u){const p=d[u]||s,b=p(e[u],t[u],u);a.isUndefined(b)&&p!==c||(n[u]=b)}),n}const Ge=e=>{const t=q({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=x.from(i),t.url=$e(Xe(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let d;if(a.isFormData(n)){if(T.hasStandardBrowserEnv||T.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((d=i.getContentType())!==!1){const[l,...u]=d?d.split(";").map(p=>p.trim()).filter(Boolean):[];i.setContentType([l||"multipart/form-data",...u].join("; "))}}if(T.hasStandardBrowserEnv&&(r&&a.isFunction(r)&&(r=r(t)),r||r!==!1&&wn(t.url))){const l=s&&o&&gn.read(o);l&&i.set(s,l)}return t},Rn=typeof XMLHttpRequest<"u",Tn=Rn&&function(e){return new Promise(function(n,r){const s=Ge(e);let o=s.data;const i=x.from(s.headers).normalize();let{responseType:c,onUploadProgress:d,onDownloadProgress:l}=s,u,p,b,E,f;function y(){E&&E(),f&&f(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let m=new XMLHttpRequest;m.open(s.method.toUpperCase(),s.url,!0),m.timeout=s.timeout;function w(){if(!m)return;const R=x.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),A={data:!c||c==="text"||c==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:R,config:e,request:m};Ke(function(k){n(k),y()},function(k){r(k),y()},A),m=null}"onloadend"in m?m.onloadend=w:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(w)},m.onabort=function(){m&&(r(new h("Request aborted",h.ECONNABORTED,e,m)),m=null)},m.onerror=function(){r(new h("Network Error",h.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let N=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const A=s.transitional||Je;s.timeoutErrorMessage&&(N=s.timeoutErrorMessage),r(new h(N,A.clarifyTimeoutError?h.ETIMEDOUT:h.ECONNABORTED,e,m)),m=null},o===void 0&&i.setContentType(null),"setRequestHeader"in m&&a.forEach(i.toJSON(),function(N,A){m.setRequestHeader(A,N)}),a.isUndefined(s.withCredentials)||(m.withCredentials=!!s.withCredentials),c&&c!=="json"&&(m.responseType=s.responseType),l&&([b,f]=V(l,!0),m.addEventListener("progress",b)),d&&m.upload&&([p,E]=V(d),m.upload.addEventListener("progress",p),m.upload.addEventListener("loadend",E)),(s.cancelToken||s.signal)&&(u=R=>{m&&(r(!R||R.type?new B(null,e,m):R),m.abort(),m=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const S=hn(s.url);if(S&&T.protocols.indexOf(S)===-1){r(new h("Unsupported protocol "+S+":",h.ERR_BAD_REQUEST,e));return}m.send(o||null)})},An=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(l){if(!s){s=!0,c();const u=l instanceof Error?l:this.reason;r.abort(u instanceof h?u:new B(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,o(new h(`timeout ${t} of ms exceeded`,h.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(o):l.removeEventListener("abort",o)}),e=null)};e.forEach(l=>l.addEventListener("abort",o));const{signal:d}=r;return d.unsubscribe=()=>a.asap(c),d}},On=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},xn=async function*(e,t){for await(const n of Ln(e))yield*On(n,t)},Ln=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Le=(e,t,n,r)=>{const s=xn(e,t);let o=0,i,c=d=>{i||(i=!0,r&&r(d))};return new ReadableStream({async pull(d){try{const{done:l,value:u}=await s.next();if(l){c(),d.close();return}let p=u.byteLength;if(n){let b=o+=p;n(b)}d.enqueue(new Uint8Array(u))}catch(l){throw c(l),l}},cancel(d){return c(d),s.return()}},{highWaterMark:2})},ee=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ze=ee&&typeof ReadableStream=="function",Cn=ee&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Qe=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Nn=Ze&&Qe(()=>{let e=!1;const t=new Request(T.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ce=64*1024,ce=Ze&&Qe(()=>a.isReadableStream(new Response("").body)),W={stream:ce&&(e=>e.body)};ee&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!W[t]&&(W[t]=a.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new h(`Response type '${t}' is not supported`,h.ERR_NOT_SUPPORT,r)})})})(new Response);const Fn=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(T.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await Cn(e)).byteLength},kn=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??Fn(t)},Dn=ee&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:d,responseType:l,headers:u,withCredentials:p="same-origin",fetchOptions:b}=Ge(e);l=l?(l+"").toLowerCase():"text";let E=An([s,o&&o.toAbortSignal()],i),f;const y=E&&E.unsubscribe&&(()=>{E.unsubscribe()});let m;try{if(d&&Nn&&n!=="get"&&n!=="head"&&(m=await kn(u,r))!==0){let A=new Request(t,{method:"POST",body:r,duplex:"half"}),F;if(a.isFormData(r)&&(F=A.headers.get("content-type"))&&u.setContentType(F),A.body){const[k,z]=Ae(m,V(Oe(d)));r=Le(A.body,Ce,k,z)}}a.isString(p)||(p=p?"include":"omit");const w="credentials"in Request.prototype;f=new Request(t,{...b,signal:E,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:w?p:void 0});let S=await fetch(f,b);const R=ce&&(l==="stream"||l==="response");if(ce&&(c||R&&y)){const A={};["status","statusText","headers"].forEach(we=>{A[we]=S[we]});const F=a.toFiniteNumber(S.headers.get("content-length")),[k,z]=c&&Ae(F,V(Oe(c),!0))||[];S=new Response(Le(S.body,Ce,k,()=>{z&&z(),y&&y()}),A)}l=l||"text";let N=await W[a.findKey(W,l)||"text"](S,e);return!R&&y&&y(),await new Promise((A,F)=>{Ke(A,F,{data:N,headers:x.from(S.headers),status:S.status,statusText:S.statusText,config:e,request:f})})}catch(w){throw y&&y(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new h("Network Error",h.ERR_NETWORK,e,f),{cause:w.cause||w}):h.from(w,w&&w.code,e,f)}}),le={http:Vt,xhr:Tn,fetch:Dn};a.forEach(le,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ne=e=>`- ${e}`,Pn=e=>a.isFunction(e)||e===null||e===!1,Ye={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!Pn(n)&&(r=le[(i=String(n)).toLowerCase()],r===void 0))throw new h(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([c,d])=>`adapter ${c} `+(d===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Ne).join(`
`):" "+Ne(o[0]):"as no adapter specified";throw new h("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:le};function se(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new B(null,e)}function Fe(e){return se(e),e.headers=x.from(e.headers),e.data=re.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ye.getAdapter(e.adapter||M.adapter)(e).then(function(r){return se(e),r.data=re.call(e,e.transformResponse,r),r.headers=x.from(r.headers),r},function(r){return We(r)||(se(e),r&&r.response&&(r.response.data=re.call(e,e.transformResponse,r.response),r.response.headers=x.from(r.response.headers))),Promise.reject(r)})}const et="1.10.0",te={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{te[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const ke={};te.transitional=function(t,n,r){function s(o,i){return"[Axios v"+et+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new h(s(i," has been removed"+(n?" in "+n:"")),h.ERR_DEPRECATED);return n&&!ke[i]&&(ke[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};te.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function qn(e,t,n){if(typeof e!="object")throw new h("options must be an object",h.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const c=e[o],d=c===void 0||i(c,o,e);if(d!==!0)throw new h("option "+o+" must be "+d,h.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new h("Unknown option "+o,h.ERR_BAD_OPTION)}}const J={assertOptions:qn,validators:te},C=J.validators;let P=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Re,response:new Re}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=q(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&J.assertOptions(r,{silentJSONParsing:C.transitional(C.boolean),forcedJSONParsing:C.transitional(C.boolean),clarifyTimeoutError:C.transitional(C.boolean)},!1),s!=null&&(a.isFunction(s)?n.paramsSerializer={serialize:s}:J.assertOptions(s,{encode:C.function,serialize:C.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),J.assertOptions(n,{baseUrl:C.spelling("baseURL"),withXsrfToken:C.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[n.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],f=>{delete o[f]}),n.headers=x.concat(i,o);const c=[];let d=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(d=d&&y.synchronous,c.unshift(y.fulfilled,y.rejected))});const l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let u,p=0,b;if(!d){const f=[Fe.bind(this),void 0];for(f.unshift.apply(f,c),f.push.apply(f,l),b=f.length,u=Promise.resolve(n);p<b;)u=u.then(f[p++],f[p++]);return u}b=c.length;let E=n;for(p=0;p<b;){const f=c[p++],y=c[p++];try{E=f(E)}catch(m){y.call(this,m);break}}try{u=Fe.call(this,E)}catch(f){return Promise.reject(f)}for(p=0,b=l.length;p<b;)u=u.then(l[p++],l[p++]);return u}getUri(t){t=q(this.defaults,t);const n=Xe(t.baseURL,t.url,t.allowAbsoluteUrls);return $e(n,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){P.prototype[t]=function(n,r){return this.request(q(r||{},{method:t,url:n,data:(r||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(q(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}P.prototype[t]=n(),P.prototype[t+"Form"]=n(!0)});let _n=class tt{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new B(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new tt(function(s){t=s}),cancel:t}}};function Un(e){return function(n){return e.apply(null,n)}}function Bn(e){return a.isObject(e)&&e.isAxiosError===!0}const ue={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ue).forEach(([e,t])=>{ue[t]=e});function nt(e){const t=new P(e),n=De(P.prototype.request,t);return a.extend(n,P.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return nt(q(e,s))},n}const g=nt(M);g.Axios=P;g.CanceledError=B;g.CancelToken=_n;g.isCancel=We;g.VERSION=et;g.toFormData=Y;g.AxiosError=h;g.Cancel=g.CanceledError;g.all=function(t){return Promise.all(t)};g.spread=Un;g.isAxiosError=Bn;g.mergeConfig=q;g.AxiosHeaders=x;g.formToJSON=e=>Ve(a.isHTMLForm(e)?new FormData(e):e);g.getAdapter=Ye.getAdapter;g.HttpStatusCode=ue;g.default=g;const{Axios:nr,AxiosError:rr,CanceledError:sr,isCancel:or,CancelToken:ir,VERSION:ar,all:cr,Cancel:lr,isAxiosError:ur,spread:dr,toFormData:fr,AxiosHeaders:pr,HttpStatusCode:mr,formToJSON:hr,getAdapter:yr,mergeConfig:br}=g;window.axios=g;document.addEventListener("DOMContentLoaded",()=>{vn(),jn(),zn(),document.querySelector(".dashboard-layout")&&Xn()});function vn(){console.log("Indah Berkah Abadi - Sistem Inventori initialized");const e=document.querySelector('meta[name="csrf-token"]');e&&window.axios&&(window.axios.defaults.headers.common["X-CSRF-TOKEN"]=e.getAttribute("content"))}function jn(){const e=document.querySelector('form[action*="login"]');e&&e.addEventListener("submit",In),document.querySelectorAll("form").forEach(n=>{n.querySelectorAll("input[required], select[required]").forEach(s=>{s.addEventListener("blur",rt),s.addEventListener("input",st)})})}function In(e){const n=e.target.querySelector('button[type="submit"]');n&&(n.disabled=!0,n.innerHTML=`
            <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Memproses...
        `,setTimeout(()=>{n.disabled&&(n.disabled=!1,n.innerHTML="Masuk")},5e3))}function rt(e){const t=e.target,n=t.value.trim();st(e);let r=!0,s="";return t.hasAttribute("required")&&!n?(r=!1,s="Field ini wajib diisi"):t.type==="email"&&n&&!ot(n)?(r=!1,s="Format email tidak valid"):t.type==="password"&&n&&n.length<6&&(r=!1,s="Kata sandi minimal 6 karakter"),r||Mn(t,s),r}function st(e){const t=e.target;t.classList.remove("form-input-error");const n=t.parentNode.querySelector(".form-error");n&&n.remove()}function Mn(e,t){e.classList.add("form-input-error");const n=document.createElement("p");n.className="form-error",n.textContent=t,e.parentNode.appendChild(n)}function ot(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function zn(){document.querySelectorAll('a[href^="#"]').forEach(s=>{s.addEventListener("click",function(o){o.preventDefault();const i=document.querySelector(this.getAttribute("href"));i&&i.scrollIntoView({behavior:"smooth",block:"start"})})}),document.querySelectorAll(".alert").forEach(s=>{setTimeout(()=>{s.style.opacity="0",setTimeout(()=>{s.remove()},300)},5e3)});const n=document.querySelector("[data-mobile-menu-toggle]"),r=document.querySelector("[data-mobile-menu]");n&&r&&n.addEventListener("click",()=>{r.classList.toggle("hidden")})}function Hn(e){e&&e.classList.add("loading")}function $n(e){e&&e.classList.remove("loading")}function Jn(e,t="info"){const n=document.createElement("div");n.className=`alert alert-${t} fixed top-4 right-4 z-50 max-w-sm`,n.textContent=e,document.body.appendChild(n),setTimeout(()=>{n.style.opacity="0",setTimeout(()=>{n.remove()},300)},5e3)}function Vn(e){return new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(e)}function Wn(e,t=null){const n=t||me();return new Intl.DateTimeFormat("id-ID",{year:"numeric",month:"long",day:"numeric",timeZone:n}).format(new Date(e))}function Kn(e,t=null){const n=t||me();return new Intl.DateTimeFormat("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",timeZone:n}).format(new Date(e))}function me(){const e=sessionStorage.getItem("user_timezone");return e||Intl.DateTimeFormat().resolvedOptions().timeZone||"Asia/Jakarta"}function Xn(){console.log("Dashboard initialized"),Gn(),Zn(),Qn()}function Gn(){const e=document.querySelector(".sidebar"),t=document.querySelector(".sidebar-overlay"),n=document.querySelector(".sidebar-toggle");n&&e&&t&&(n.addEventListener("click",r=>{r.preventDefault(),r.stopPropagation(),he()}),t.addEventListener("click",r=>{r.preventDefault(),_()}),document.addEventListener("click",r=>{window.innerWidth<=768&&!e.contains(r.target)&&!n.contains(r.target)&&_()}),document.addEventListener("keydown",r=>{r.key==="Escape"&&window.innerWidth<=768&&_()})),window.addEventListener("resize",()=>{it()})}function he(){const e=document.querySelector(".sidebar"),t=document.querySelector(".sidebar-overlay");e&&t&&(e.classList.contains("show")?_():ye())}function ye(){const e=document.querySelector(".sidebar"),t=document.querySelector(".sidebar-overlay");if(e&&t){e.classList.add("show"),t.classList.add("show"),document.body.classList.add("sidebar-open"),window.innerWidth<=768&&(document.body.style.overflow="hidden",document.body.style.position="fixed",document.body.style.width="100%");const n=e.querySelector('a, button, [tabindex]:not([tabindex="-1"])');n&&setTimeout(()=>n.focus(),100)}}function _(){const e=document.querySelector(".sidebar"),t=document.querySelector(".sidebar-overlay");if(e&&t){e.classList.remove("show"),t.classList.remove("show"),document.body.classList.remove("sidebar-open"),document.body.style.overflow="",document.body.style.position="",document.body.style.width="";const n=document.querySelector(".sidebar-toggle");n&&window.innerWidth<=768&&n.focus()}}function it(){const e=document.querySelector(".sidebar"),t=document.querySelector(".sidebar-overlay");window.innerWidth>768&&e&&t&&(e.classList.remove("show"),t.classList.remove("show"),document.body.classList.remove("sidebar-open"),document.body.style.overflow="",document.body.style.position="",document.body.style.width="")}function Zn(){const e=document.querySelector(".user-menu-button"),t=document.querySelector(".user-menu-dropdown");e&&t&&(e.addEventListener("click",n=>{n.preventDefault(),n.stopPropagation(),be()}),document.addEventListener("click",n=>{!e.contains(n.target)&&!t.contains(n.target)&&K()}),document.addEventListener("keydown",n=>{n.key==="Escape"&&K()}))}function be(){const e=document.querySelector(".user-menu-button"),t=document.querySelector(".user-menu-dropdown"),n=e==null?void 0:e.querySelector("svg");if(t)if(t.classList.contains("show"))K();else{K(),t.classList.add("show"),e&&e.setAttribute("aria-expanded","true"),n&&(n.style.transform="rotate(180deg)");const s=t.querySelector("a, button");s&&setTimeout(()=>s.focus(),100)}}function K(){const e=document.querySelectorAll(".user-menu-dropdown, .dropdown-menu"),t=document.querySelector(".user-menu-button"),n=t==null?void 0:t.querySelector("svg");e.forEach(r=>{r.classList.remove("show")}),t&&t.setAttribute("aria-expanded","false"),n&&(n.style.transform="rotate(0deg)")}function Qn(){window.addEventListener("resize",()=>{it()}),Yn()}function Yn(){const e=document.querySelectorAll("table");e.forEach(t=>{if(window.innerWidth<=768&&t.classList.add("mobile-table"),!t.parentElement.classList.contains("table-responsive")){const n=document.createElement("div");n.className="table-responsive",t.parentNode.insertBefore(n,t),n.appendChild(t)}}),window.addEventListener("resize",()=>{e.forEach(t=>{window.innerWidth<=768?t.classList.add("mobile-table"):t.classList.remove("mobile-table")})})}window.IndahBerkahApp={showLoading:Hn,hideLoading:$n,showNotification:Jn,formatCurrency:Vn,formatDate:Wn,formatDateTime:Kn,getUserTimezone:me,validateField:rt,isValidEmail:ot,toggleSidebar:he,openSidebar:ye,closeSidebar:_,toggleUserMenu:be};window.openSidebar=ye;window.closeSidebar=_;window.toggleSidebar=he;window.toggleUserMenu=be;
