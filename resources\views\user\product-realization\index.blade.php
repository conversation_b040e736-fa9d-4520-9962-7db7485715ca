@extends('layouts.user')

@section('title', 'Realisasi Produk - Dashboard Toko')

@push('styles')
<!-- Independent CSS for User Dashboard Forms -->
<link rel="stylesheet" href="{{ asset('css/user-dashboard-forms.css') }}">
@endpush

@section('content')
<div class="user-dashboard-container">
    <div class="space-y-6">
        <!-- Header -->
        <div class="user-dashboard-header">
            <div class="user-dashboard-header-content">
                <h1 class="user-dashboard-header-title">Realisasi Produk</h1>
                <p class="user-dashboard-header-subtitle">Pantau perbedaan antara jumlah distribusi yang direncanakan dengan yang diterima di {{ $stats['store_name'] }}</p>
            </div>
        </div>

        <!-- Time Period Filter -->
        @include('user.components.time-period-filter')

        <!-- Statistics Cards -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ number_format($stats['total_distributions']) }}</div>
                            <div class="text-xs text-gray-600">Total Distribusi</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ number_format($stats['total_planned']) }}</div>
                            <div class="text-xs text-gray-600">Jumlah Direncanakan</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ number_format($stats['total_received']) }}</div>
                            <div class="text-xs text-gray-600">Jumlah Diterima</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ $stats['realization_percentage'] }}%</div>
                            <div class="text-xs text-gray-600">Tingkat Realisasi</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="user-dashboard-search-container">
            <div class="user-dashboard-card-content">
                <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-700">Filter Tambahan</span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">Gunakan filter di bawah untuk mempersempit pencarian dalam periode waktu yang dipilih</p>
                </div>
                <form method="GET" action="{{ route('user.product-realization.index') }}" class="user-dashboard-search-form user-dashboard-form-container-enhanced">
                    <!-- Preserve time period parameter -->
                    @if(request('period'))
                        <input type="hidden" name="period" value="{{ request('period') }}">
                    @endif
                    <div class="user-dashboard-filter-group user-dashboard-filter-group-enhanced">
                        <label for="product-filter" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Produk</label>
                        <select id="product-filter" name="product" class="user-dashboard-form-select user-dashboard-form-select-enhanced">
                            <option value="">Semua Produk</option>
                            @foreach($products as $product)
                                <option value="{{ $product->id }}" {{ $productFilter == $product->id ? 'selected' : '' }}>
                                    {{ $product->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="user-dashboard-filter-group user-dashboard-filter-group-enhanced">
                        <label for="date-from" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Dari Tanggal</label>
                        <input id="date-from" type="date" name="date_from" value="{{ $dateFrom }}" class="user-dashboard-form-input user-dashboard-form-date-enhanced">
                    </div>
                    <div class="user-dashboard-filter-group user-dashboard-filter-group-enhanced">
                        <label for="date-to" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Sampai Tanggal</label>
                        <input id="date-to" type="date" name="date_to" value="{{ $dateTo }}" class="user-dashboard-form-input user-dashboard-form-date-enhanced">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center w-full user-dashboard-btn-sm user-dashboard-form-submit-enhanced">Filter</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Product Realization Table -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Data Realisasi Produk</h2>
                <p class="user-dashboard-card-description">{{ $distributions->total() }} data realisasi ditemukan</p>
            </div>
            <div class="user-dashboard-card-content">
                @if($distributions->count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                                <th class="px-6 py-3">Produk</th>
                                <th class="px-6 py-3">Tanggal Distribusi</th>
                                <th class="px-6 py-3">Jumlah Direncanakan</th>
                                <th class="px-6 py-3">Jumlah Diterima</th>
                                <th class="px-6 py-3">Selisih</th>
                                <th class="px-6 py-3">Catatan</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($distributions as $distribution)
                            <tr class="bg-white border-b hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="font-medium text-gray-900">{{ $distribution->product->name }}</div>
                                </td>
                                <td class="px-6 py-4">{{ $distribution->date_distributed->format('d/m/Y') }}</td>
                                <td class="px-6 py-4">
                                    <span class="font-medium text-gray-900">{{ number_format($distribution->quantity) }}</span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="font-medium text-gray-900">{{ number_format($distribution->received_quantity) }}</span>
                                </td>
                                <td class="px-6 py-4">
                                    @php
                                        $difference = $distribution->received_quantity - $distribution->quantity;
                                    @endphp
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if($difference == 0) bg-green-100 text-green-800
                                        @elseif($difference > 0) bg-blue-100 text-blue-800
                                        @else bg-red-100 text-red-800
                                        @endif">
                                        @if($difference == 0)
                                            Sesuai
                                        @elseif($difference > 0)
                                            +{{ number_format($difference) }}
                                        @else
                                            {{ number_format($difference) }}
                                        @endif
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-600">
                                        {{ $distribution->notes ?: '-' }}
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Tidak ada data realisasi produk</h3>
                    <p class="mt-1 text-sm text-gray-500">Data akan muncul setelah ada distribusi yang dikonfirmasi</p>
                </div>
                @endif

                <!-- Pagination -->
                @if($distributions->hasPages())
                <div class="mt-4">
                    {{ $distributions->appends(request()->query())->links('user.components.pagination') }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@push('scripts')
<!-- Independent JavaScript for User Dashboard Forms -->
<script src="{{ asset('js/user-dashboard-forms.js') }}"></script>
@endpush

@endsection
