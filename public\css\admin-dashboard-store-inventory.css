/* PT. Indah Berkah Abadi - Admin Dashboard Store Inventory */
/* Simplified styling aligned with standard admin dashboard patterns */

/* ===== STANDARD MODAL SYSTEM ===== */

.admin-dashboard-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.admin-dashboard-modal.active {
    display: flex;
}

.admin-dashboard-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    margin: 20px;
}

/* Large modal variant for enhanced visibility */
.admin-dashboard-modal-large {
    max-width: 800px;
    width: 95%;
}

/* Enhanced modal styling for better prominence */
.admin-dashboard-modal-enhanced {
    background: rgba(0, 0, 0, 0.6);
}

.admin-dashboard-modal-enhanced .admin-dashboard-modal-content {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid #e5e7eb;
}

.admin-dashboard-modal-header {
    padding: 24px 24px 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.admin-dashboard-modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

.admin-dashboard-modal-close {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.admin-dashboard-modal-close:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.admin-dashboard-modal-body {
    padding: 0 24px 24px 24px;
}

.admin-dashboard-modal-footer {
    padding: 0 24px 24px 24px;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* ===== STANDARD FORM STYLING ===== */

.admin-dashboard-form-group {
    margin-bottom: 1.5rem;
}

.admin-dashboard-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.admin-dashboard-input,
.admin-dashboard-select,
.admin-dashboard-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.admin-dashboard-input:focus,
.admin-dashboard-select:focus,
.admin-dashboard-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-dashboard-textarea {
    resize: vertical;
    min-height: 80px;
}

/* ===== STANDARD BUTTON STYLING ===== */

.admin-dashboard-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
}

.admin-dashboard-btn-primary {
    background: #3b82f6;
    color: white;
}

.admin-dashboard-btn-primary:hover {
    background: #2563eb;
}

.admin-dashboard-btn-secondary {
    background: #6b7280;
    color: white;
}

.admin-dashboard-btn-secondary:hover {
    background: #4b5563;
}

.admin-dashboard-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* ===== STANDARD TABLE STYLING ===== */

.admin-dashboard-table-container {
    overflow-x: auto;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    background-color: white;
}

.admin-dashboard-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.admin-dashboard-table-header {
    background: #f8fafc;
}

.admin-dashboard-table-th {
    padding: 1rem 1.5rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
    font-size: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

.admin-dashboard-table-body {
    background-color: white;
}

.admin-dashboard-table-row {
    border-bottom: 1px solid #f3f4f6;
}

.admin-dashboard-table-row:hover {
    background-color: #f8fafc;
}

.admin-dashboard-table-td {
    padding: 1rem 1.5rem;
    color: #1f2937;
    vertical-align: middle;
}

/* ===== STANDARD BADGE STYLING ===== */

.admin-dashboard-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 9999px;
    text-transform: uppercase;
}

/* ===== STANDARD EMPTY STATES ===== */

.admin-dashboard-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1.5rem;
    text-align: center;
}

.admin-dashboard-empty-icon {
    margin: 0 auto 1rem auto !important;
    width: 3rem !important;
    height: 3rem !important;
    color: #d1d5db !important;
    display: block !important;
}

.admin-dashboard-empty-icon svg {
    width: 100% !important;
    height: 100% !important;
    max-width: 3rem !important;
    max-height: 3rem !important;
}

.admin-dashboard-empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 0.5rem 0;
}

.admin-dashboard-empty-description {
    color: #6b7280;
    margin: 0;
    font-size: 0.875rem;
}

/* ===== STANDARD NOTIFICATION SYSTEM ===== */

.admin-dashboard-notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    max-width: 400px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.admin-dashboard-notification-success {
    border-left: 4px solid #10b981;
}

.admin-dashboard-notification-error {
    border-left: 4px solid #ef4444;
}

.admin-dashboard-notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
}

/* ===== MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
    .admin-dashboard-modal {
        padding: 0.5rem;
    }

    .admin-dashboard-modal-content {
        max-width: 100%;
        margin: 0;
        border-radius: 8px;
    }

    .admin-dashboard-modal-header,
    .admin-dashboard-modal-body,
    .admin-dashboard-modal-footer {
        padding: 1rem;
    }

    .admin-dashboard-modal-footer {
        flex-direction: column;
        gap: 0.5rem;
    }

    .admin-dashboard-table-th,
    .admin-dashboard-table-td {
        padding: 0.75rem;
        font-size: 0.8rem;
    }

    .admin-dashboard-notification {
        top: 0.5rem;
        right: 0.5rem;
        left: 0.5rem;
        max-width: none;
    }
}
