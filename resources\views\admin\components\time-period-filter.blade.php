{{-- Time Period Filter Component for Admin Dashboard --}}
@php
    $currentPeriod = request('period', 'month');
    $periods = [
        'day' => [
            'label' => 'Hari Ini',
            'icon' => 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
        ],
        'week' => [
            'label' => 'Minggu Ini',
            'icon' => 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
        ],
        'month' => [
            'label' => 'Bulan Ini',
            'icon' => 'M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2'
        ],
        'all' => [
            'label' => 'Semua Waktu',
            'icon' => 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
        ]
    ];
@endphp

<div class="admin-dashboard-card">
    <div class="admin-dashboard-card-content">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Filter Periode Waktu</h3>
                <p class="text-sm text-gray-600">Pilih periode waktu untuk melihat data</p>
            </div>
            <form method="GET" class="flex items-center gap-2">
                <select name="period" class="bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
                    @foreach($periods as $key => $period)
                        <option value="{{ $key }}" @if($currentPeriod === $key) selected @endif>{{ $period['label'] }}</option>
                    @endforeach
                </select>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm">Terapkan</button>
            </form>
        </div>
        
        {{-- Period Info Display --}}
        <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div class="flex items-center gap-2">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm font-medium text-blue-800">
                    Menampilkan data untuk: {{ $periods[$currentPeriod]['label'] }}
                </span>
            </div>
            @if($currentPeriod !== 'all')
                <div class="text-xs text-blue-600 mt-1">
                    @switch($currentPeriod)
                        @case('day')
                            {{ now()->format('d F Y') }}
                            @break
                        @case('week')
                            {{ now()->startOfWeek()->format('d M') }} - {{ now()->endOfWeek()->format('d M Y') }}
                            @break
                        @case('month')
                            {{ now()->format('F Y') }}
                            @break
                    @endswitch
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.admin-dashboard-time-period-btn {
    @apply inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-xl border-2 transition-all duration-300 focus:outline-none focus:ring-3 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-105 min-h-[44px] min-w-[44px] justify-center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-dashboard-time-period-btn-active {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white border-blue-700 shadow-lg;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.admin-dashboard-time-period-btn-inactive {
    @apply bg-white text-gray-700 border-gray-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-dashboard-time-period-btn-inactive:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.admin-dashboard-time-period-btn:active {
    transform: scale(0.98);
}

@media (max-width: 640px) {
    .admin-dashboard-time-period-btn {
        @apply px-3 py-2 text-xs min-h-[44px] min-w-[44px];
    }
}

@media (max-width: 480px) {
    .admin-dashboard-time-period-btn {
        @apply px-2 py-2 text-xs gap-1;
    }
}
</style>
