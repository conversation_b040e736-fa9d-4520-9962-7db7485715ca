<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, check if there are any invalid source values and clean them up
        $invalidSources = DB::table('stock_movements')
            ->whereNotIn('source', ['supplier', 'store', 'adjustment', 'distribution'])
            ->get();

        if ($invalidSources->count() > 0) {
            // Update any invalid source values to 'adjustment' as a safe fallback
            DB::table('stock_movements')
                ->whereNotIn('source', ['supplier', 'store', 'adjustment', 'distribution'])
                ->update(['source' => 'adjustment']);
        }

        // Add 'store_adjustment' and 'return_to_supplier' to the source enum in stock_movements table
        DB::statement("ALTER TABLE stock_movements MODIFY COLUMN source ENUM('supplier', 'store', 'adjustment', 'distribution', 'store_adjustment', 'return_to_supplier')");

        // Ensure the table has all necessary indexes for performance
        Schema::table('stock_movements', function (Blueprint $table) {
            // Add index for store-level movements if not exists
            if (!$this->indexExists('stock_movements', 'stock_movements_reference_type_reference_id_index')) {
                $table->index(['reference_type', 'reference_id'], 'stock_movements_reference_type_reference_id_index');
            }

            // Add index for source filtering if not exists
            if (!$this->indexExists('stock_movements', 'stock_movements_source_index')) {
                $table->index('source', 'stock_movements_source_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, update any records using the new enum values to safe fallbacks
        DB::table('stock_movements')
            ->where('source', 'store_adjustment')
            ->update(['source' => 'adjustment']);

        DB::table('stock_movements')
            ->where('source', 'return_to_supplier')
            ->update(['source' => 'adjustment']);

        // Remove 'store_adjustment' and 'return_to_supplier' from the source enum
        DB::statement("ALTER TABLE stock_movements MODIFY COLUMN source ENUM('supplier', 'store', 'adjustment', 'distribution')");

        // Remove the indexes we added
        Schema::table('stock_movements', function (Blueprint $table) {
            if ($this->indexExists('stock_movements', 'stock_movements_reference_type_reference_id_index')) {
                $table->dropIndex('stock_movements_reference_type_reference_id_index');
            }

            if ($this->indexExists('stock_movements', 'stock_movements_source_index')) {
                $table->dropIndex('stock_movements_source_index');
            }
        });
    }

    /**
     * Check if an index exists on a table.
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = DB::select("SHOW INDEX FROM {$table}");
        foreach ($indexes as $indexInfo) {
            if ($indexInfo->Key_name === $index) {
                return true;
            }
        }
        return false;
    }
};
