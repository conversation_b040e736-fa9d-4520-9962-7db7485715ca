@extends('layouts.user')

@section('title', 'Kelola Stok Produk - Dashboard Toko')

@push('styles')
<!-- Independent CSS for User Dashboard Product Management -->
<link rel="stylesheet" href="{{ asset('css/user-dashboard-forms.css') }}">
<link rel="stylesheet" href="{{ asset('css/user-dashboard-product-management.css') }}">
@endpush

@section('content')
<div class="user-dashboard-container">
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Kelola Stok Produk</h1>
            <p class="user-dashboard-header-subtitle">{{ $product->name }} - {{ $store->name }}</p>
        </div>
        <div class="user-dashboard-header-actions">
            <a href="{{ route('user.products.index') }}" class="user-dashboard-btn-secondary user-dashboard-btn-sm">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m0 7h18"></path>
                </svg>
                Kembali
            </a>
        </div>
    </div>

    <!-- Current Stock Info -->
    <div class="user-dashboard-product-current-stock">
        <div class="user-dashboard-product-stock-card">
            <div class="user-dashboard-product-stock-header">
                <h3 class="user-dashboard-product-stock-title">Stok Saat Ini</h3>
                <svg class="user-dashboard-product-stock-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div class="user-dashboard-product-stock-value">{{ number_format($storeStock->quantity) }} unit</div>
            <div class="user-dashboard-product-stock-description">
                Terakhir diperbarui: {{ $storeStock->updated_at->format('d/m/Y H:i') }}
            </div>
        </div>
    </div>

    <!-- Error Messages Display -->
    @if ($errors->any())
        <div class="user-dashboard-product-error-container">
            <div class="user-dashboard-product-error-header">
                <svg class="user-dashboard-product-error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h4 class="user-dashboard-product-error-title">Terjadi Kesalahan</h4>
            </div>
            <ul class="user-dashboard-product-error-list">
                @foreach ($errors->all() as $error)
                    <li class="user-dashboard-product-error-item">{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Success Messages Display -->
    @if (session('success'))
        <div class="user-dashboard-product-success-container">
            <div class="user-dashboard-product-success-header">
                <svg class="user-dashboard-product-success-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <h4 class="user-dashboard-product-success-title">Berhasil</h4>
            </div>
            <p class="user-dashboard-product-success-message">{{ session('success') }}</p>
        </div>
    @endif

    <!-- Info Messages Display -->
    @if (session('info'))
        <div class="user-dashboard-product-info-container">
            <div class="user-dashboard-product-info-header">
                <svg class="user-dashboard-product-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h4 class="user-dashboard-product-info-title">Informasi</h4>
            </div>
            <p class="user-dashboard-product-info-message">{{ session('info') }}</p>
        </div>
    @endif

    <!-- Error Messages Display -->
    @if (session('error'))
        <div class="user-dashboard-product-error-container">
            <div class="user-dashboard-product-error-header">
                <svg class="user-dashboard-product-error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h4 class="user-dashboard-product-error-title">Terjadi Kesalahan</h4>
            </div>
            <p class="user-dashboard-product-error-item">{{ session('error') }}</p>
        </div>
    @endif

    <!-- Adjustment Form -->
    <div class="user-dashboard-product-form-wrapper">
        <form method="POST" action="{{ route('user.products.adjust', $product) }}" class="user-dashboard-product-form" id="productAdjustmentForm">
            @csrf
            
            <div class="user-dashboard-form-container-enhanced">
                <!-- Adjustment Type -->
                <div class="user-dashboard-form-group-enhanced">
                    <label class="user-dashboard-form-label-enhanced">Jenis Penyesuaian</label>
                    <select name="adjustment_type" 
                            class="user-dashboard-form-input-enhanced @error('adjustment_type') user-dashboard-form-input-error @enderror"
                            onchange="updateQuantityField()" 
                            id="adjustmentType">
                        <option value="">Pilih jenis penyesuaian...</option>
                        <option value="subtract" {{ old('adjustment_type') == 'subtract' ? 'selected' : '' }}>
                            Kurangi Stok
                        </option>
                        <option value="set_zero" {{ old('adjustment_type') == 'set_zero' ? 'selected' : '' }}>
                            Habis (Set ke 0)
                        </option>
                    </select>
                    @error('adjustment_type')
                        <div class="user-dashboard-form-error-enhanced">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Quantity Input -->
                <div class="user-dashboard-form-group-enhanced" id="quantityGroup" style="display: none;">
                    <label class="user-dashboard-form-label-enhanced" id="quantityLabel">Jumlah Pengurangan</label>
                    <input type="number" 
                           name="quantity" 
                           value="{{ old('quantity') }}"
                           min="1" 
                           max="{{ $storeStock->quantity }}"
                           placeholder="Masukkan jumlah yang akan dikurangi..."
                           class="user-dashboard-form-number-enhanced @error('quantity') user-dashboard-form-input-error @enderror"
                           onchange="updatePreview()"
                           id="quantityInput">
                    <div class="user-dashboard-product-quantity-hint">
                        Maksimal: {{ number_format($storeStock->quantity) }} unit
                    </div>
                    @error('quantity')
                        <div class="user-dashboard-form-error-enhanced">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Notes -->
                <div class="user-dashboard-form-group-enhanced">
                    <label class="user-dashboard-form-label-enhanced">Catatan (Opsional)</label>
                    <textarea name="notes" 
                              rows="3"
                              placeholder="Tambahkan catatan untuk penyesuaian stok ini..."
                              class="user-dashboard-form-textarea-enhanced @error('notes') user-dashboard-form-input-error @enderror">{{ old('notes') }}</textarea>
                    @error('notes')
                        <div class="user-dashboard-form-error-enhanced">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Preview -->
                <div class="user-dashboard-product-preview" id="adjustmentPreview" style="display: none;">
                    <div class="user-dashboard-product-preview-header">
                        <h4 class="user-dashboard-product-preview-title">Pratinjau Penyesuaian</h4>
                    </div>
                    <div class="user-dashboard-product-preview-content">
                        <div class="user-dashboard-product-preview-row">
                            <span class="user-dashboard-product-preview-label">Stok Saat Ini:</span>
                            <span class="user-dashboard-product-preview-value" id="currentStock">{{ number_format($storeStock->quantity) }} unit</span>
                        </div>
                        <div class="user-dashboard-product-preview-row">
                            <span class="user-dashboard-product-preview-label">Perubahan:</span>
                            <span class="user-dashboard-product-preview-value" id="changeAmount">-</span>
                        </div>
                        <div class="user-dashboard-product-preview-row user-dashboard-product-preview-result">
                            <span class="user-dashboard-product-preview-label">Stok Setelah Penyesuaian:</span>
                            <span class="user-dashboard-product-preview-value" id="newStock">-</span>
                        </div>
                    </div>
                </div>

                <!-- Validation Warning -->
                <div class="user-dashboard-product-validation-warning" id="validationWarning" style="display: none;">
                    <div class="user-dashboard-product-warning-icon">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="user-dashboard-product-warning-content">
                        <h5 class="user-dashboard-product-warning-title">Peringatan</h5>
                        <p class="user-dashboard-product-warning-message" id="validationMessage"></p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="user-dashboard-form-button-group-enhanced">
                    <button type="submit"
                            class="user-dashboard-form-submit-enhanced"
                            id="submitButton">
                        <svg class="user-dashboard-product-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Simpan Penyesuaian
                    </button>
                    <a href="{{ route('user.products.index') }}" class="user-dashboard-form-cancel-enhanced">
                        <svg class="user-dashboard-product-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Batal
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Stock Reduction Confirmation Modal -->
    @include('user.components.confirmation-modal', [
        'modalId' => 'stock-reduction-modal',
        'title' => 'Konfirmasi Pengurangan Stok',
        'type' => 'warning'
    ])

    <!-- Set to Zero Confirmation Modal -->
    @include('user.components.confirmation-modal', [
        'modalId' => 'set-zero-modal',
        'title' => 'Konfirmasi Habis Stok',
        'type' => 'danger'
    ])
</div>

@push('scripts')
<script>
const currentStockValue = {{ $storeStock->quantity }};

function updateQuantityField() {
    const adjustmentType = document.getElementById('adjustmentType').value;
    const quantityGroup = document.getElementById('quantityGroup');
    const quantityInput = document.getElementById('quantityInput');
    
    if (adjustmentType === 'subtract') {
        quantityGroup.style.display = 'block';
        quantityInput.required = true;
    } else if (adjustmentType === 'set_zero') {
        quantityGroup.style.display = 'none';
        quantityInput.required = false;
        quantityInput.value = '';
    } else {
        quantityGroup.style.display = 'none';
        quantityInput.required = false;
    }
    
    updatePreview();
}

function updatePreview() {
    const adjustmentType = document.getElementById('adjustmentType').value;
    const quantity = parseInt(document.getElementById('quantityInput').value) || 0;
    const preview = document.getElementById('adjustmentPreview');
    const changeAmount = document.getElementById('changeAmount');
    const newStock = document.getElementById('newStock');
    const validationWarning = document.getElementById('validationWarning');
    const validationMessage = document.getElementById('validationMessage');
    const submitButton = document.getElementById('submitButton');

    if (!adjustmentType) {
        preview.style.display = 'none';
        validationWarning.style.display = 'none';
        // Don't disable submit button when no adjustment type is selected
        // Let server-side validation handle this
        submitButton.disabled = false;
        submitButton.classList.remove('user-dashboard-form-submit-disabled');
        return;
    }

    let change = 0;
    let newStockValue = 0;
    let hasValidationError = false;
    let errorMessage = '';

    switch(adjustmentType) {
        case 'subtract':
            if (quantity > 0) {
                change = -quantity;
                newStockValue = Math.max(0, currentStockValue - quantity);
                changeAmount.textContent = `-${quantity.toLocaleString()} unit`;
                changeAmount.className = 'user-dashboard-product-preview-value user-dashboard-product-change-negative';

                if (quantity > currentStockValue) {
                    hasValidationError = true;
                    errorMessage = `Jumlah pengurangan (${quantity} unit) melebihi stok yang tersedia (${currentStockValue} unit). Silakan kurangi jumlah atau pilih "Habis" untuk mengosongkan stok.`;
                }
            } else if (adjustmentType === 'subtract') {
                // Show message when subtract is selected but no quantity entered
                hasValidationError = true;
                errorMessage = 'Silakan masukkan jumlah yang akan dikurangi.';
            }
            break;
        case 'set_zero':
            change = -currentStockValue;
            newStockValue = 0;
            changeAmount.textContent = `-${currentStockValue.toLocaleString()} unit`;
            changeAmount.className = 'user-dashboard-product-preview-value user-dashboard-product-change-negative';
            break;
    }

    newStock.textContent = `${newStockValue.toLocaleString()} unit`;
    newStock.className = newStockValue === 0 ?
        'user-dashboard-product-preview-value user-dashboard-product-stock-zero' :
        'user-dashboard-product-preview-value';

    // Show/hide validation warning but don't disable submit button
    // Let server-side validation handle the final validation
    if (hasValidationError) {
        validationMessage.textContent = errorMessage;
        validationWarning.style.display = 'block';
        // Only disable submit for client-side validation warnings, not errors
        // This allows server-side validation to provide more detailed feedback
        submitButton.disabled = false;
        submitButton.classList.remove('user-dashboard-form-submit-disabled');
    } else {
        validationWarning.style.display = 'none';
        submitButton.disabled = false;
        submitButton.classList.remove('user-dashboard-form-submit-disabled');
    }

    preview.style.display = 'block';
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateQuantityField();

    // Override form submission to use custom modal
    const form = document.getElementById('productAdjustmentForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const adjustmentType = document.getElementById('adjustmentType').value;
            const quantity = parseInt(document.getElementById('quantityInput').value) || 0;
            const productName = '{{ $product->name }}';
            const currentStock = {{ $storeStock->quantity }};

            if (!adjustmentType) {
                alert('Silakan pilih jenis penyesuaian stok');
                return;
            }

            if (adjustmentType === 'subtract') {
                if (quantity <= 0) {
                    alert('Silakan masukkan jumlah yang akan dikurangi');
                    return;
                }

                if (quantity > currentStock) {
                    alert(`Jumlah pengurangan (${quantity}) melebihi stok tersedia (${currentStock})`);
                    return;
                }

                // Show stock reduction confirmation modal
                UserModalManager.showStockReductionConfirmation({
                    productName: productName,
                    quantity: quantity,
                    currentStock: currentStock,
                    onConfirm: function() {
                        // Submit the form
                        form.removeEventListener('submit', arguments.callee);
                        form.submit();
                        return true;
                    }
                });
            } else if (adjustmentType === 'set_zero') {
                // Show set to zero confirmation modal
                UserModalManager.showSetToZeroConfirmation({
                    productName: productName,
                    currentStock: currentStock,
                    onConfirm: function() {
                        // Submit the form
                        form.removeEventListener('submit', arguments.callee);
                        form.submit();
                        return true;
                    }
                });
            }
        });
    }
});
</script>
<script src="{{ asset('js/user-dashboard-product-management.js') }}"></script>
@endpush
@endsection
