<?php

return array(

    /*
    |--------------------------------------------------------------------------
    | Styles
    |--------------------------------------------------------------------------
    |
    | The default styles which will be used when parsing a view
    |
    */

    'styles'    => array(

        /*
        |--------------------------------------------------------------------------
        | Table headings
        |--------------------------------------------------------------------------
        */
        'th'    => array(
            'font' => array(
                'bold'      => true,
                'size'      => 12,
            )
        ),

        /*
        |--------------------------------------------------------------------------
        | Strong tags
        |--------------------------------------------------------------------------
        */
        'strong' => array(
            'font' => array(
                'bold'      => true,
                'size'      => 12,
            )
        ),

        /*
        |--------------------------------------------------------------------------
        | Bold tags
        |--------------------------------------------------------------------------
        */
        'b' => array(
            'font' => array(
                'bold'      => true,
                'size'      => 12,
            )
        ),

        /*
        |--------------------------------------------------------------------------
        | Italic tags
        |--------------------------------------------------------------------------
        */
        'i' => array(
            'font' => array(
                'italic'    => true,
                'size'      => 12,
            )
        ),

        /*
        |--------------------------------------------------------------------------
        | Heading 1
        |--------------------------------------------------------------------------
        */
        'h1' => array(
            'font' => array(
                'bold'      => true,
                'size'      => 24,
            )
        ),

        /*
        |--------------------------------------------------------------------------
        | Heading 2
        |--------------------------------------------------------------------------
        */
       'h2' => array(
            'font' => array(
                'bold'      => true,
                'size'      => 18,
            )
        ),

        /*
        |--------------------------------------------------------------------------
        | Heading 2
        |--------------------------------------------------------------------------
        */
       'h3' => array(
            'font' => array(
                'bold'      => true,
                'size'      => 13.5,
            )
        ),

       /*
        |--------------------------------------------------------------------------
        | Heading 4
        |--------------------------------------------------------------------------
        */
       'h4' => array(
            'font' => array(
                'bold'      => true,
                'size'      => 12,
            )
        ),

       /*
        |--------------------------------------------------------------------------
        | Heading 5
        |--------------------------------------------------------------------------
        */
       'h5' => array(
            'font' => array(
                'bold'      => true,
                'size'      => 10,
            )
        ),

       /*
        |--------------------------------------------------------------------------
        | Heading 6
        |--------------------------------------------------------------------------
        */
       'h6' => array(
            'font' => array(
                'bold'      => true,
                'size'      => 7.5,
            )
        ),

       /*
        |--------------------------------------------------------------------------
        | Hyperlinks
        |--------------------------------------------------------------------------
        */
       'a'  => array(
            'font' => array(
                'underline' => true,
                'color'     => array( 'argb' => 'FF0000FF'),
            )
        ),

       /*
        |--------------------------------------------------------------------------
        | Horizontal rules
        |--------------------------------------------------------------------------
        */
       'hr' => array(
            'borders' => array(
                'bottom' => array(
                    'style' => 'thin',
                    'color' => array('FF000000')
                ),
            )
        )
     )

);