/* PT. Indah Berkah Abadi - Admin Dashboard Product Management */
/* Independent CSS for admin product management modals and interactions */

/* ===== ADMIN MODAL SYSTEM ===== */

.admin-dashboard-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 9999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease-in-out !important;
}

.admin-dashboard-modal-overlay.admin-dashboard-modal-show {
    opacity: 1 !important;
    visibility: visible !important;
}

.admin-dashboard-modal-container {
    width: 100% !important;
    max-width: 600px !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    transform: scale(0.9) translateY(-20px) !important;
    transition: transform 0.3s ease-in-out !important;
}

.admin-dashboard-modal-overlay.admin-dashboard-modal-show .admin-dashboard-modal-container {
    transform: scale(1) translateY(0) !important;
}

.admin-dashboard-modal-content {
    background-color: #ffffff !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    overflow: hidden !important;
    position: relative !important;
}

/* Modal Type Styling */
.admin-dashboard-modal-danger {
    border-top: 4px solid #dc2626 !important;
}

.admin-dashboard-modal-warning {
    border-top: 4px solid #f59e0b !important;
}

.admin-dashboard-modal-info {
    border-top: 4px solid #3b82f6 !important;
}

/* Modal Header */
.admin-dashboard-modal-header {
    display: flex !important;
    align-items: flex-start !important;
    padding: 1.5rem !important;
    border-bottom: 1px solid #e5e7eb !important;
    gap: 1rem !important;
}

.admin-dashboard-modal-icon {
    flex-shrink: 0 !important;
    width: 3rem !important;
    height: 3rem !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.admin-dashboard-modal-icon-danger {
    background-color: #fee2e2 !important;
    color: #dc2626 !important;
}

.admin-dashboard-modal-icon-warning {
    background-color: #fef3c7 !important;
    color: #f59e0b !important;
}

.admin-dashboard-modal-icon-info {
    background-color: #dbeafe !important;
    color: #3b82f6 !important;
}

.admin-dashboard-modal-icon-svg {
    width: 1.5rem !important;
    height: 1.5rem !important;
}

.admin-dashboard-modal-title-section {
    flex: 1 !important;
}

.admin-dashboard-modal-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

.admin-dashboard-modal-subtitle {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    margin: 0.25rem 0 0 0 !important;
    line-height: 1.4 !important;
}

/* Modal Body */
.admin-dashboard-modal-body {
    padding: 1.5rem !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
}

.admin-dashboard-modal-section {
    margin-bottom: 1.5rem !important;
}

.admin-dashboard-modal-section:last-child {
    margin-bottom: 0 !important;
}

.admin-dashboard-modal-section-title {
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin: 0 0 0.75rem 0 !important;
    line-height: 1.5 !important;
}

/* Product Information */
.admin-dashboard-modal-product-info {
    background-color: #f9fafb !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
}

.admin-dashboard-modal-product-name {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    line-height: 1.5 !important;
}

/* Warning and Impact Lists */
.admin-dashboard-modal-warning-list,
.admin-dashboard-modal-impact-list {
    space-y: 0.75rem !important;
}

.admin-dashboard-modal-warning-item,
.admin-dashboard-modal-impact-item {
    display: flex !important;
    align-items: flex-start !important;
    gap: 0.75rem !important;
    padding: 0.75rem !important;
    background-color: #fef2f2 !important;
    border: 1px solid #fecaca !important;
    border-radius: 0.5rem !important;
    margin-bottom: 0.75rem !important;
}

.admin-dashboard-modal-warning-icon,
.admin-dashboard-modal-impact-icon {
    flex-shrink: 0 !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #dc2626 !important;
    margin-top: 0.125rem !important;
}

.admin-dashboard-modal-warning-content,
.admin-dashboard-modal-impact-content {
    flex: 1 !important;
}

.admin-dashboard-modal-warning-title,
.admin-dashboard-modal-impact-title {
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #991b1b !important;
    margin: 0 0 0.25rem 0 !important;
    line-height: 1.4 !important;
}

.admin-dashboard-modal-warning-description,
.admin-dashboard-modal-impact-description {
    font-size: 0.875rem !important;
    color: #7f1d1d !important;
    margin: 0 !important;
    line-height: 1.4 !important;
}

/* Confirmation Message */
.admin-dashboard-modal-confirmation-message {
    background-color: #fffbeb !important;
    border: 1px solid #fed7aa !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
}

.admin-dashboard-modal-confirmation-message p {
    font-size: 1rem !important;
    color: #92400e !important;
    margin: 0 !important;
    line-height: 1.5 !important;
    text-align: center !important;
    font-weight: 500 !important;
}

/* Modal Footer */
.admin-dashboard-modal-footer {
    display: flex !important;
    justify-content: flex-end !important;
    gap: 0.75rem !important;
    padding: 1.5rem !important;
    border-top: 1px solid #e5e7eb !important;
    background-color: #f9fafb !important;
}

/* Modal Buttons */
.admin-dashboard-modal-btn {
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem 1.5rem !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    border-radius: 0.5rem !important;
    border: 1px solid transparent !important;
    cursor: pointer !important;
    transition: all 0.2s ease-in-out !important;
    text-decoration: none !important;
    min-height: 44px !important; /* Touch-friendly */
}

.admin-dashboard-modal-btn-icon {
    width: 1rem !important;
    height: 1rem !important;
    flex-shrink: 0 !important;
}

.admin-dashboard-modal-btn-primary {
    background-color: #166534 !important;
    color: #ffffff !important;
    border-color: #166534 !important;
}

.admin-dashboard-modal-btn-primary:hover {
    background-color: #15803d !important;
    border-color: #15803d !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(22, 101, 52, 0.3) !important;
}

.admin-dashboard-modal-btn-secondary {
    background-color: #ffffff !important;
    color: #374151 !important;
    border-color: #d1d5db !important;
}

.admin-dashboard-modal-btn-secondary:hover {
    background-color: #f9fafb !important;
    border-color: #9ca3af !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.admin-dashboard-modal-btn-danger {
    background-color: #dc2626 !important;
    color: #ffffff !important;
    border-color: #dc2626 !important;
}

.admin-dashboard-modal-btn-danger:hover {
    background-color: #b91c1c !important;
    border-color: #b91c1c !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3) !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .admin-dashboard-modal-container {
        max-width: 95% !important;
        margin: 0 auto !important;
    }
    
    .admin-dashboard-modal-header {
        padding: 1rem !important;
        flex-direction: column !important;
        text-align: center !important;
        gap: 0.75rem !important;
    }
    
    .admin-dashboard-modal-body {
        padding: 1rem !important;
    }
    
    .admin-dashboard-modal-footer {
        padding: 1rem !important;
        flex-direction: column !important;
        gap: 0.5rem !important;
    }
    
    .admin-dashboard-modal-btn {
        width: 100% !important;
        justify-content: center !important;
    }
}

/* Animation Classes */
.admin-dashboard-modal-entering .admin-dashboard-modal-container {
    animation: admin-dashboard-modal-fade-in 0.3s ease-out forwards !important;
}

.admin-dashboard-modal-leaving .admin-dashboard-modal-container {
    animation: admin-dashboard-modal-fade-out 0.3s ease-in forwards !important;
}

@keyframes admin-dashboard-modal-fade-in {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes admin-dashboard-modal-fade-out {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
}
