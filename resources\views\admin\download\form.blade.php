@extends('layouts.admin')

@section('title', 'Unduh Data Excel - Indah Berkah Abadi')
@section('page-title', 'Unduh Data Excel')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Unduh Data Excel</h1>
                    <p class="text-gray-600 mt-1">Unduh data produk, toko, dan distribusi dalam format Excel</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('admin.dashboard') }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali ke Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <!-- <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900">{{ $stats['total_products'] ?? 0 }}</div>
                        <div class="text-xs text-gray-600">Total Produk</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900">{{ $stats['total_stores'] ?? 0 }}</div>
                        <div class="text-xs text-gray-600">Total Toko</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900">{{ $stats['total_distributions'] ?? 0 }}</div>
                        <div class="text-xs text-gray-600">Total Distribusi</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900">{{ $stats['recent_downloads'] ?? 0 }}</div>
                        <div class="text-xs text-gray-600">Distribusi 7 Hari</div>
                    </div>
                </div>
            </div>
        </div>
    </div> -->

    <!-- Download Form -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Pilih Rentang Tanggal</h2>
            <p class="admin-dashboard-card-description">
                Pilih periode data yang ingin diunduh. Data distribusi akan difilter berdasarkan tanggal yang dipilih.
            </p>
        </div>
        <div class="admin-dashboard-card-content">
            <form action="{{ route('admin.download.process') }}" method="POST">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <!-- Start Date -->
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Mulai <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="date"
                            id="start_date"
                            name="start_date"
                            value="{{ old('start_date', now()->subMonth()->format('Y-m-d')) }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('start_date') border-red-500 @enderror"
                            required
                        >
                        @error('start_date')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- End Date -->
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Akhir <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="date"
                            id="end_date"
                            name="end_date"
                            value="{{ old('end_date', now()->format('Y-m-d')) }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('end_date') border-red-500 @enderror"
                            required
                        >
                        @error('end_date')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Info Box -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start gap-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-blue-800 mb-2">Informasi Unduhan</h3>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• File akan berisi 3 worksheet terpisah: Data Produk, Data Toko, dan Data Distribusi</li>
                                <li>• Data produk dan toko akan mencakup semua data yang tersedia</li>
                                <li>• Data distribusi akan difilter sesuai rentang tanggal yang dipilih</li>
                                <li>• File akan diunduh dalam format Excel (.xls)</li>
                                <li>• Waktu akan ditampilkan sesuai timezone Anda: {{ auth()->user()->timezone ?? 'Asia/Jakarta' }}</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Unduh Data Excel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Unduhan Cepat</h2>
            <p class="admin-dashboard-card-description">
                Unduh data dengan periode yang sudah ditentukan
            </p>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <form action="{{ route('admin.download.process') }}" method="POST">
                    @csrf
                    <input type="hidden" name="start_date" value="{{ now()->startOfMonth()->format('Y-m-d') }}">
                    <input type="hidden" name="end_date" value="{{ now()->format('Y-m-d') }}">
                    <button type="submit" class="w-full bg-white border-2 border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-left group">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Bulan Ini</h3>
                                <p class="text-sm text-gray-500">{{ now()->startOfMonth()->format('d/m/Y') }} - {{ now()->format('d/m/Y') }}</p>
                            </div>
                        </div>
                    </button>
                </form>

                <form action="{{ route('admin.download.process') }}" method="POST">
                    @csrf
                    <input type="hidden" name="start_date" value="{{ now()->subDays(30)->format('Y-m-d') }}">
                    <input type="hidden" name="end_date" value="{{ now()->format('Y-m-d') }}">
                    <button type="submit" class="w-full bg-white border-2 border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-left group">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center group-hover:bg-orange-200 transition-colors">
                                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">30 Hari Terakhir</h3>
                                <p class="text-sm text-gray-500">{{ now()->subDays(30)->format('d/m/Y') }} - {{ now()->format('d/m/Y') }}</p>
                            </div>
                        </div>
                    </button>
                </form>

                <form action="{{ route('admin.download.process') }}" method="POST">
                    @csrf
                    <input type="hidden" name="start_date" value="{{ now()->subDays(7)->format('Y-m-d') }}">
                    <input type="hidden" name="end_date" value="{{ now()->format('Y-m-d') }}">
                    <button type="submit" class="w-full bg-white border-2 border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-left group">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">7 Hari Terakhir</h3>
                                <p class="text-sm text-gray-500">{{ now()->subDays(7)->format('d/m/Y') }} - {{ now()->format('d/m/Y') }}</p>
                            </div>
                        </div>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection
