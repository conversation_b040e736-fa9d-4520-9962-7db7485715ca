<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create warehouses table for admin data scoping
        Schema::create('warehouses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('location');
            $table->string('code')->unique(); // Warehouse code for identification
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
        });

        // Add warehouse_id to users table for admin scoping
        Schema::table('users', function (Blueprint $table) {
            $table->foreignUuid('warehouse_id')->nullable()->after('store_id')->constrained('warehouses')->onDelete('set null');
            $table->foreignUuid('supplier_id')->nullable()->after('warehouse_id')->constrained('suppliers')->onDelete('set null');
        });

        // Add warehouse_id to warehouse_stock table for proper scoping
        Schema::table('warehouse_stock', function (Blueprint $table) {
            $table->foreignUuid('warehouse_id')->nullable()->after('store_id')->constrained('warehouses')->onDelete('cascade');
        });

        // Add supplier_id to products table for supplier ownership
        Schema::table('products', function (Blueprint $table) {
            $table->foreignUuid('supplier_id')->nullable()->after('name')->constrained('suppliers')->onDelete('set null');
        });

        // Add warehouse_id to distributions table for warehouse-specific distributions
        Schema::table('distributions', function (Blueprint $table) {
            $table->foreignUuid('warehouse_id')->nullable()->after('store_id')->constrained('warehouses')->onDelete('cascade');
        });

        // Add warehouse_id to supplier_deliveries table for warehouse-specific deliveries
        Schema::table('supplier_deliveries', function (Blueprint $table) {
            $table->foreignUuid('warehouse_id')->nullable()->after('received_by')->constrained('warehouses')->onDelete('cascade');
        });

        // Add warehouse_id to returns table for warehouse-specific returns
        Schema::table('returns', function (Blueprint $table) {
            $table->foreignUuid('warehouse_id')->nullable()->after('supplier_delivery_id')->constrained('warehouses')->onDelete('cascade');
        });

        // Add warehouse_id to stock_movements table for warehouse-specific movements
        Schema::table('stock_movements', function (Blueprint $table) {
            $table->foreignUuid('warehouse_id')->nullable()->after('reference_id')->constrained('warehouses')->onDelete('cascade');
        });

        // Create default warehouses
        $this->createDefaultWarehouses();

        // Update existing data to maintain current functionality
        $this->updateExistingData();
    }

    /**
     * Create default warehouses for existing admin accounts
     */
    private function createDefaultWarehouses(): void
    {
        // Create warehouses for existing admin accounts
        $warehouses = [
            [
                'id' => DB::raw('UUID()'),
                'name' => 'Gudang Pusat 1',
                'location' => 'Balikpapan',
                'code' => 'WH001',
                'description' => 'Gudang pusat untuk Administrator 1',
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => DB::raw('UUID()'),
                'name' => 'Gudang Pusat 2', 
                'location' => 'Balikpapan',
                'code' => 'WH002',
                'description' => 'Gudang pusat untuk Administrator 2',
                'status' => 'active',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($warehouses as $warehouse) {
            DB::table('warehouses')->insert($warehouse);
        }
    }

    /**
     * Update existing data to maintain current functionality
     */
    private function updateExistingData(): void
    {
        // Get the created warehouses
        $warehouse1 = DB::table('warehouses')->where('code', 'WH001')->first();
        $warehouse2 = DB::table('warehouses')->where('code', 'WH002')->first();
        
        // Get existing suppliers
        $supplier1 = DB::table('suppliers')->where('name', 'PT. Sumber Makmur')->first();
        $supplier2 = DB::table('suppliers')->where('name', 'CV. Berkah Jaya')->first();

        if ($warehouse1 && $warehouse2) {
            // Assign admin accounts to warehouses
            DB::table('users')
                ->where('email', '<EMAIL>')
                ->update(['warehouse_id' => $warehouse1->id]);
                
            DB::table('users')
                ->where('email', '<EMAIL>')
                ->update(['warehouse_id' => $warehouse2->id]);

            // Split existing warehouse stock between warehouses (50/50 split)
            $existingStock = DB::table('warehouse_stock')->whereNull('store_id')->get();
            
            foreach ($existingStock as $stock) {
                $halfQuantity = intval($stock->quantity / 2);
                $remainingQuantity = $stock->quantity - $halfQuantity;
                
                // Update original record for warehouse 1
                DB::table('warehouse_stock')
                    ->where('id', $stock->id)
                    ->update([
                        'warehouse_id' => $warehouse1->id,
                        'quantity' => $halfQuantity
                    ]);
                
                // Create new record for warehouse 2
                if ($remainingQuantity > 0) {
                    DB::table('warehouse_stock')->insert([
                        'id' => DB::raw('UUID()'),
                        'product_id' => $stock->product_id,
                        'quantity' => $remainingQuantity,
                        'store_id' => null,
                        'warehouse_id' => $warehouse2->id,
                        'date_received' => $stock->date_received,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }

            // Update existing distributions to be associated with warehouse 1 (default)
            DB::table('distributions')->update(['warehouse_id' => $warehouse1->id]);

            // Update existing supplier deliveries to be associated with warehouse 1 (default)
            DB::table('supplier_deliveries')->update(['warehouse_id' => $warehouse1->id]);

            // Update existing returns to be associated with appropriate warehouses
            DB::table('returns')->update(['warehouse_id' => $warehouse1->id]);

            // Update existing stock movements to be associated with warehouse 1 (default)
            DB::table('stock_movements')->update(['warehouse_id' => $warehouse1->id]);
        }

        if ($supplier1 && $supplier2) {
            // Assign supplier admin accounts to suppliers
            DB::table('users')
                ->where('email', '<EMAIL>')
                ->update(['supplier_id' => $supplier1->id]);
                
            DB::table('users')
                ->where('email', '<EMAIL>')
                ->update(['supplier_id' => $supplier2->id]);

            // Split existing products between suppliers (50/50 split)
            $existingProducts = DB::table('products')->get();
            $productCount = $existingProducts->count();
            
            foreach ($existingProducts as $index => $product) {
                // Assign first half to supplier 1, second half to supplier 2
                $supplierId = $index < ($productCount / 2) ? $supplier1->id : $supplier2->id;
                
                DB::table('products')
                    ->where('id', $product->id)
                    ->update(['supplier_id' => $supplierId]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove foreign key constraints and columns in reverse order
        Schema::table('stock_movements', function (Blueprint $table) {
            $table->dropForeign(['warehouse_id']);
            $table->dropColumn('warehouse_id');
        });

        Schema::table('returns', function (Blueprint $table) {
            $table->dropForeign(['warehouse_id']);
            $table->dropColumn('warehouse_id');
        });

        Schema::table('supplier_deliveries', function (Blueprint $table) {
            $table->dropForeign(['warehouse_id']);
            $table->dropColumn('warehouse_id');
        });

        Schema::table('distributions', function (Blueprint $table) {
            $table->dropForeign(['warehouse_id']);
            $table->dropColumn('warehouse_id');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['supplier_id']);
            $table->dropColumn('supplier_id');
        });

        Schema::table('warehouse_stock', function (Blueprint $table) {
            $table->dropForeign(['warehouse_id']);
            $table->dropColumn('warehouse_id');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['warehouse_id']);
            $table->dropForeign(['supplier_id']);
            $table->dropColumn(['warehouse_id', 'supplier_id']);
        });

        Schema::dropIfExists('warehouses');
    }
};
