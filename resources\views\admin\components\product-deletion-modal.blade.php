{{--
    Enhanced Product Deletion Confirmation Modal
    PT. Indah Berkah Abadi - Admin Dashboard
    Modern, professional design with smooth animations
--}}

<!-- Modern Product Deletion Modal -->
<div id="product-deletion-modal"
     class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 opacity-0 invisible transition-all duration-300 ease-out"
     style="display: none;">

    <!-- Modal Container -->
    <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full transform scale-95 transition-transform duration-300 ease-out">

        <!-- Modal Header -->
        <div class="flex items-center gap-4 p-6 border-b border-gray-100">
            <!-- Warning Icon -->
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>

            <!-- Title Section -->
            <div class="flex-1">
                <h3 class="text-lg font-bold text-gray-900">
                    Konfirmasi Hapus Produk
                </h3>
                <p class="text-sm text-gray-600 mt-1">
                    Tindakan ini tidak dapat dibatalkan
                </p>
            </div>

            <!-- Close Button -->
            <button type="button"
                    onclick="AdminProductManager.closeDeleteModal()"
                    class="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors duration-200">
                <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="p-6 space-y-6">

            <!-- Product Information Card -->
            <div class="bg-gray-50 rounded-xl p-4">
                <h4 class="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Produk yang akan dihapus
                </h4>
                <div class="bg-white rounded-lg p-3 border border-gray-200">
                    <div class="font-semibold text-gray-900" id="product-deletion-modal-product-name">
                        <!-- Product name will be inserted here -->
                    </div>
                </div>
            </div>

            <!-- Warning Section -->
            <div id="product-deletion-modal-warnings" class="hidden">
                <h4 class="text-sm font-semibold text-red-700 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    Peringatan Data Terkait
                </h4>
                <div class="space-y-2" id="product-deletion-modal-warning-list">
                    <!-- Warning items will be inserted here -->
                </div>
            </div>

            <!-- Impact Section -->
            <div id="product-deletion-modal-impact" class="hidden">
                <h4 class="text-sm font-semibold text-orange-700 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Dampak Penghapusan
                </h4>
                <div class="space-y-2" id="product-deletion-modal-impact-list">
                    <!-- Impact items will be inserted here -->
                </div>
            </div>

            <!-- Confirmation Message -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                <div class="flex items-start gap-3">
                    <div class="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-yellow-800 mb-1">Konfirmasi Penghapusan</p>
                        <p class="text-sm text-yellow-700" id="product-deletion-modal-message">
                            Apakah Anda yakin ingin menghapus produk ini?
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-100 bg-gray-50 rounded-b-2xl">
            <!-- Cancel Button -->
            <button type="button"
                    onclick="AdminProductManager.closeDeleteModal()"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Batal
            </button>

            <!-- Delete Button -->
            <button type="button"
                    onclick="AdminProductManager.confirmDelete()"
                    id="product-deletion-modal-confirm-btn"
                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded-lg hover:bg-red-700 hover:border-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200 flex items-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Ya, Hapus Produk
            </button>
        </div>

    </div>
</div>

<!-- Hidden form for deletion -->
<form id="product-deletion-form" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>
