<?php

namespace App\Services;

use Illuminate\Support\Facades\Response;
use Carbon\Carbon;
use ZipArchive;

class ExcelExportService
{
    /**
     * Generate Excel file using XML format that Excel can read properly
     */
    public function generateExcelFile($worksheets, $filename, $userTimezone = 'Asia/Jakarta')
    {
        $xmlContent = $this->generateExcelXMLContent($worksheets, $userTimezone);

        $headers = [
            'Content-Type' => 'application/vnd.ms-excel',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        return Response::make($xmlContent, 200, $headers);
    }

    /**
     * Generate Excel XML content that Excel can read properly
     */
    private function generateExcelXMLContent($worksheets, $userTimezone)
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Data Export Indah Berkah Abadi</Title>
  <Author>Indah Berkah Abadi</Author>
  <Created>' . now()->toISOString() . '</Created>
 </DocumentProperties>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12000</WindowHeight>
  <WindowWidth>15000</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s62">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="16" ss:Color="#FFFFFF" ss:Bold="1"/>
   <Interior ss:Color="#1E40AF" ss:Pattern="Solid"/>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="s63">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="12" ss:Color="#FFFFFF" ss:Bold="1"/>
   <Interior ss:Color="#3B82F6" ss:Pattern="Solid"/>
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
  </Style>
  <Style ss:ID="s64">
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Bold="1"/>
   <Interior ss:Color="#F3F4F6" ss:Pattern="Solid"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
  </Style>
  <Style ss:ID="s65">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
  </Style>
  <Style ss:ID="s66">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Right"/>
  </Style>
  <Style ss:ID="s67">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Alignment ss:Horizontal="Center"/>
  </Style>
 </Styles>';

        // Generate each worksheet
        foreach ($worksheets as $worksheet) {
            $xml .= $this->generateWorksheet($worksheet, $userTimezone);
        }

        $xml .= '</Workbook>';

        return $xml;
    }

    /**
     * Generate a single worksheet
     */
    private function generateWorksheet($worksheet, $userTimezone)
    {
        $xml = '<Worksheet ss:Name="' . htmlspecialchars($worksheet['name']) . '">
  <Table>';

        // Worksheet Header
        $colSpan = count($worksheet['headers']) - 1;
        $xml .= '<Row>
   <Cell ss:MergeAcross="' . $colSpan . '" ss:StyleID="s62"><Data ss:Type="String">' . htmlspecialchars($worksheet['title']) . '</Data></Cell>
  </Row>';

        if (isset($worksheet['subtitle'])) {
            $xml .= '<Row>
   <Cell ss:MergeAcross="' . $colSpan . '" ss:StyleID="s62"><Data ss:Type="String">' . htmlspecialchars($worksheet['subtitle']) . '</Data></Cell>
  </Row>';
        }

        $xml .= '<Row>
   <Cell ss:MergeAcross="' . $colSpan . '" ss:StyleID="s62"><Data ss:Type="String">Diunduh pada: ' . now()->setTimezone($userTimezone)->format('d/m/Y H:i:s') . ' ' . $userTimezone . '</Data></Cell>
  </Row>
  <Row></Row>';

        // Table Headers
        $xml .= '<Row>';
        foreach ($worksheet['headers'] as $header) {
            $xml .= '<Cell ss:StyleID="s64"><Data ss:Type="String">' . htmlspecialchars($header) . '</Data></Cell>';
        }
        $xml .= '</Row>';

        // Table Data
        foreach ($worksheet['data'] as $row) {
            $xml .= '<Row>';
            foreach ($row as $index => $cell) {
                $styleId = 's65'; // Default style

                // Apply specific styles based on data type or position
                if ($index === 0 && is_numeric($cell)) {
                    $styleId = 's67'; // Center align for row numbers
                } elseif (is_numeric($cell) && $index > 0) {
                    $styleId = 's66'; // Right align for numbers
                } elseif (strpos($cell, '/') !== false && strpos($cell, ':') !== false) {
                    $styleId = 's67'; // Center align for dates
                }

                $dataType = is_numeric($cell) ? 'Number' : 'String';
                $xml .= '<Cell ss:StyleID="' . $styleId . '"><Data ss:Type="' . $dataType . '">' . htmlspecialchars($cell) . '</Data></Cell>';
            }
            $xml .= '</Row>';
        }

        $xml .= '</Table>
 </Worksheet>';

        return $xml;
    }

    /**
     * Create filename with date range
     */
    public function createFilename($prefix, $startDate = null, $endDate = null)
    {
        $filename = $prefix;

        if ($startDate && $endDate) {
            $filename .= '_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d');
        } elseif ($startDate) {
            $filename .= '_' . $startDate->format('Y-m-d');
        } else {
            $filename .= '_' . now()->format('Y-m-d');
        }

        return $filename . '.xls';
    }



    /**
     * Format date for display
     */
    public function formatDate($date, $userTimezone = 'Asia/Jakarta', $format = 'd/m/Y H:i:s')
    {
        if (!$date) return '';

        return Carbon::parse($date)->setTimezone($userTimezone)->format($format);
    }

    /**
     * Format date for display (date only)
     */
    public function formatDateOnly($date, $userTimezone = 'Asia/Jakarta')
    {
        return $this->formatDate($date, $userTimezone, 'd/m/Y');
    }
}
