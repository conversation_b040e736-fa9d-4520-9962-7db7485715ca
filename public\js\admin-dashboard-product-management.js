/**
 * Admin Dashboard Product Management
 * PT. Indah Berkah Abadi - Inventory System
 * Handles product deletion with comprehensive warning system
 */
(function() {
    'use strict';

    const AdminProductManager = {
        // Configuration
        config: {
            modalClass: 'admin-dashboard-modal-overlay',
            showClass: 'admin-dashboard-modal-show',
            enteringClass: 'admin-dashboard-modal-entering',
            leavingClass: 'admin-dashboard-modal-leaving',
            animationDuration: 300
        },

        // Current state
        currentProductId: null,
        currentProductName: null,
        deleteForm: null,

        // Initialize the manager
        init: function() {
            this.bindGlobalEvents();
            this.deleteForm = document.getElementById('product-deletion-form');
        },

        // Bind global event listeners
        bindGlobalEvents: function() {
            const self = this;

            // Handle escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    self.closeDeleteModal();
                }
            });

            // Handle click outside modal
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains(self.config.modalClass)) {
                    self.closeDeleteModal();
                }
            });
        },

        // Show delete confirmation modal
        showDeleteConfirmation: function(productId, productName, warehouseStock, distributionsCount, stockOpnameCount) {
            this.currentProductId = productId;
            this.currentProductName = productName;

            // Update modal content
            this.populateDeleteModal(productName, warehouseStock, distributionsCount, stockOpnameCount);

            // Show modal
            this.showModal('product-deletion-modal');
        },

        // Populate delete modal with product information
        populateDeleteModal: function(productName, warehouseStock, distributionsCount, stockOpnameCount) {
            // Update product name
            const productNameElement = document.getElementById('product-deletion-modal-product-name');
            if (productNameElement) {
                productNameElement.textContent = productName;
            }

            // Check if there are blocking reasons
            const hasWarnings = warehouseStock > 0 || distributionsCount > 0 || stockOpnameCount > 0;

            if (hasWarnings) {
                this.showWarningsSection(warehouseStock, distributionsCount, stockOpnameCount);
                this.updateConfirmationMessage(true);
            } else {
                this.hideWarningsSection();
                this.updateConfirmationMessage(false);
            }
        },

        // Show warnings section with modern design
        showWarningsSection: function(warehouseStock, distributionsCount, stockOpnameCount) {
            const warningsSection = document.getElementById('product-deletion-modal-warnings');
            const impactSection = document.getElementById('product-deletion-modal-impact');
            const warningsList = document.getElementById('product-deletion-modal-warning-list');
            const impactList = document.getElementById('product-deletion-modal-impact-list');

            if (!warningsSection || !impactSection || !warningsList || !impactList) return;

            // Clear existing content
            warningsList.innerHTML = '';
            impactList.innerHTML = '';

            // Add warnings with modern styling
            if (warehouseStock > 0) {
                this.addModernWarningItem(warningsList, 'Stok Gudang', `Produk memiliki ${warehouseStock.toLocaleString()} unit stok di gudang pusat`, 'warning');
                this.addModernImpactItem(impactList, 'Stok Gudang Hilang', `${warehouseStock.toLocaleString()} unit stok akan hilang dari sistem`, 'danger');
            }

            if (distributionsCount > 0) {
                this.addModernWarningItem(warningsList, 'Riwayat Distribusi', `Produk memiliki ${distributionsCount} riwayat distribusi`, 'warning');
                this.addModernImpactItem(impactList, 'Data Distribusi Hilang', `${distributionsCount} riwayat distribusi akan terhapus permanen`, 'danger');
            }

            if (stockOpnameCount > 0) {
                this.addModernWarningItem(warningsList, 'Riwayat Stock Opname', `Produk memiliki ${stockOpnameCount} riwayat stock opname`, 'warning');
                this.addModernImpactItem(impactList, 'Data Stock Opname Hilang', `${stockOpnameCount} riwayat stock opname akan terhapus permanen`, 'danger');
            }

            // Show sections with animation
            warningsSection.classList.remove('hidden');
            impactSection.classList.remove('hidden');
        },

        // Hide warnings section
        hideWarningsSection: function() {
            const warningsSection = document.getElementById('product-deletion-modal-warnings');
            const impactSection = document.getElementById('product-deletion-modal-impact');

            if (warningsSection) warningsSection.classList.add('hidden');
            if (impactSection) impactSection.classList.add('hidden');
        },

        // Add modern warning item to list
        addModernWarningItem: function(container, title, description, type = 'warning') {
            const item = document.createElement('div');
            item.className = 'bg-yellow-50 border border-yellow-200 rounded-lg p-3 flex items-start gap-3';
            item.innerHTML = `
                <div class="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-yellow-800 text-sm">${title}</div>
                    <div class="text-yellow-700 text-sm mt-1">${description}</div>
                </div>
            `;
            container.appendChild(item);
        },

        // Add modern impact item to list
        addModernImpactItem: function(container, title, description, type = 'danger') {
            const item = document.createElement('div');
            item.className = 'bg-red-50 border border-red-200 rounded-lg p-3 flex items-start gap-3';
            item.innerHTML = `
                <div class="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-red-800 text-sm">${title}</div>
                    <div class="text-red-700 text-sm mt-1">${description}</div>
                </div>
            `;
            container.appendChild(item);
        },

        // Legacy functions for backward compatibility
        addWarningItem: function(container, title, description) {
            this.addModernWarningItem(container, title, description);
        },

        addImpactItem: function(container, title, description) {
            this.addModernImpactItem(container, title, description);
        },

        // Update confirmation message with modern styling
        updateConfirmationMessage: function(hasWarnings) {
            const messageElement = document.getElementById('product-deletion-modal-message');
            if (!messageElement) return;

            if (hasWarnings) {
                messageElement.innerHTML = `
                    <strong>Peringatan:</strong> Menghapus produk ini akan menghilangkan semua data terkait secara permanen.<br>
                    <span class="font-semibold">Apakah Anda yakin ingin melanjutkan?</span>
                `;
            } else {
                messageElement.innerHTML = `
                    Apakah Anda yakin ingin menghapus produk ini? <br>
                    <span class="font-semibold">Tindakan ini tidak dapat dibatalkan.</span>
                `;
            }
        },

        // Show modal with modern animation
        showModal: function(modalId) {
            const modal = document.getElementById(modalId);
            if (!modal) {
                console.error('Modal not found:', modalId);
                return;
            }

            // Prevent body scroll
            document.body.style.overflow = 'hidden';

            // Show modal with smooth animation
            modal.style.display = 'flex';

            // Force reflow
            modal.offsetHeight;

            // Animate in
            modal.style.opacity = '1';
            modal.style.visibility = 'visible';

            const modalContent = modal.querySelector('div > div');
            if (modalContent) {
                modalContent.style.transform = 'scale(1)';
            }

            modal.setAttribute('aria-hidden', 'false');
        },

        // Close delete modal with smooth animation
        closeDeleteModal: function() {
            const modal = document.getElementById('product-deletion-modal');
            if (!modal) return;

            // Animate out
            modal.style.opacity = '0';
            modal.style.visibility = 'hidden';

            const modalContent = modal.querySelector('div > div');
            if (modalContent) {
                modalContent.style.transform = 'scale(0.95)';
            }

            setTimeout(() => {
                modal.style.display = 'none';
                modal.setAttribute('aria-hidden', 'true');

                // Restore body scroll
                document.body.style.overflow = '';

                // Reset state
                this.currentProductId = null;
                this.currentProductName = null;
            }, this.config.animationDuration);
        },

        // Confirm deletion and submit form
        confirmDelete: function() {
            if (!this.currentProductId || !this.deleteForm) return;

            // Update form action
            this.deleteForm.action = `/admin/products/${this.currentProductId}`;
            
            // Submit form
            this.deleteForm.submit();
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            AdminProductManager.init();
        });
    } else {
        AdminProductManager.init();
    }

    // Expose to global scope
    window.AdminProductManager = AdminProductManager;

})();
