<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Interactions - PT. Indah Be<PERSON></title>
    
    <!-- Include the form CSS -->
    <link rel="stylesheet" href="css/user-dashboard-forms.css">
    
    <!-- Basic styling for test page -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
            line-height: 1.6;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            background: #fafafa;
        }
        
        .test-title {
            font-size: 1.75rem;
            font-weight: bold;
            color: #111827;
            margin-bottom: 1rem;
        }
        
        .test-subtitle {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
        }
        
        .test-description {
            color: #6b7280;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }
        
        .sidebar-mock {
            position: fixed;
            top: 0;
            left: 0;
            width: 16rem;
            height: 100vh;
            background-color: #166534;
            z-index: 1000;
            padding: 1rem;
            color: white;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }
        
        .sidebar-mock.show {
            transform: translateX(0);
        }
        
        .main-content {
            margin-left: 0;
            transition: margin-left 0.3s ease;
        }
        
        .main-content.sidebar-open {
            margin-left: 17rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .test-card {
            background: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }
        
        .status-indicator {
            padding: 0.5rem;
            border-radius: 0.375rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .test-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
            font-size: 0.875rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .test-button:hover {
            background-color: #2563eb;
        }
        
        .toggle-button {
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background-color: #166534;
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        @media (max-width: 768px) {
            .main-content.sidebar-open {
                margin-left: 0;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Toggle Button -->
    <button class="toggle-button" onclick="toggleSidebar()">
        ☰ Toggle Sidebar
    </button>

    <!-- Mock Sidebar -->
    <div class="sidebar-mock" id="mockSidebar">
        <h3>PT. Indah Berkah Abadi</h3>
        <p>Mock Sidebar (z-index: 1000)</p>
        <p style="font-size: 0.75rem; margin-top: 1rem;">
            This sidebar should remain accessible while all form elements work properly below it.
        </p>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="test-container">
            <h1 class="test-title">Form Interaction Comprehensive Test</h1>
            <p class="test-description">
                This page tests all form interaction fixes for PT. Indah Berkah Abadi user dashboard.
                The sidebar should remain accessible (highest z-index) while all form elements work properly.
            </p>

            <!-- Test Section 1: Input Fields -->
            <div class="test-section">
                <h2 class="test-subtitle">1. Input Field Tests</h2>
                <p class="test-description">Test all types of input fields with enhanced classes.</p>
                
                <div class="test-grid">
                    <div class="test-card">
                        <h3>Text Input</h3>
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="text-input" class="user-dashboard-form-label-enhanced">Text Input</label>
                            <input type="text" id="text-input" class="user-dashboard-form-input-enhanced" placeholder="Type here..." onchange="logInteraction('Text Input', this.value)">
                        </div>
                    </div>
                    
                    <div class="test-card">
                        <h3>Number Input</h3>
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="number-input" class="user-dashboard-form-label-enhanced">Number Input</label>
                            <input type="number" id="number-input" class="user-dashboard-form-number-enhanced" placeholder="Enter number..." onchange="logInteraction('Number Input', this.value)">
                        </div>
                    </div>
                    
                    <div class="test-card">
                        <h3>Password Input</h3>
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="password-input" class="user-dashboard-form-label-enhanced">Password Input</label>
                            <input type="password" id="password-input" class="user-dashboard-form-password-enhanced" placeholder="Enter password..." onchange="logInteraction('Password Input', '***')">
                        </div>
                    </div>
                    
                    <div class="test-card">
                        <h3>Date Input</h3>
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="date-input" class="user-dashboard-form-label-enhanced">Date Input</label>
                            <input type="date" id="date-input" class="user-dashboard-form-date-enhanced" onchange="logInteraction('Date Input', this.value)">
                        </div>
                    </div>
                    
                    <div class="test-card">
                        <h3>Textarea</h3>
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="textarea-input" class="user-dashboard-form-label-enhanced">Textarea</label>
                            <textarea id="textarea-input" class="user-dashboard-form-textarea-enhanced" rows="3" placeholder="Enter text..." onchange="logInteraction('Textarea', this.value)"></textarea>
                        </div>
                    </div>
                    
                    <div class="test-card">
                        <h3>Select Dropdown</h3>
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="select-input" class="user-dashboard-form-label-enhanced">Select Dropdown</label>
                            <select id="select-input" class="user-dashboard-form-select-enhanced" onchange="logInteraction('Select Dropdown', this.value)">
                                <option value="">Choose option...</option>
                                <option value="option1">Option 1</option>
                                <option value="option2">Option 2</option>
                                <option value="option3">Option 3</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Section 2: Button Tests -->
            <div class="test-section">
                <h2 class="test-subtitle">2. Button Tests</h2>
                <p class="test-description">Test submit and cancel buttons with enhanced classes.</p>
                
                <div class="test-grid">
                    <div class="test-card">
                        <h3>Submit Buttons</h3>
                        <div class="user-dashboard-form-button-group-enhanced">
                            <button type="submit" class="user-dashboard-form-submit-enhanced" onclick="logInteraction('Submit Button', 'clicked')">
                                Submit Form
                            </button>
                            <button type="button" class="user-dashboard-form-submit-enhanced" onclick="logInteraction('Primary Button', 'clicked')">
                                Primary Action
                            </button>
                        </div>
                    </div>
                    
                    <div class="test-card">
                        <h3>Cancel Buttons</h3>
                        <div class="user-dashboard-form-button-group-enhanced">
                            <button type="button" class="user-dashboard-form-cancel-enhanced" onclick="logInteraction('Cancel Button', 'clicked')">
                                Cancel
                            </button>
                            <button type="button" class="user-dashboard-form-cancel-enhanced" onclick="logInteraction('Secondary Button', 'clicked')">
                                Secondary Action
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Section 3: Form Container Tests -->
            <div class="test-section">
                <h2 class="test-subtitle">3. Complete Form Test</h2>
                <p class="test-description">Test a complete form with all elements together.</p>
                
                <div class="test-card">
                    <form class="user-dashboard-form-container-enhanced" onsubmit="return testFormSubmit(event)">
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="form-name" class="user-dashboard-form-label-enhanced">Full Name</label>
                            <input type="text" id="form-name" name="name" class="user-dashboard-form-input-enhanced" required>
                        </div>
                        
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="form-quantity" class="user-dashboard-form-label-enhanced">Quantity</label>
                            <input type="number" id="form-quantity" name="quantity" class="user-dashboard-form-number-enhanced" min="0" required>
                        </div>
                        
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="form-date" class="user-dashboard-form-label-enhanced">Date</label>
                            <input type="date" id="form-date" name="date" class="user-dashboard-form-date-enhanced" required>
                        </div>
                        
                        <div class="user-dashboard-form-group-enhanced">
                            <label for="form-notes" class="user-dashboard-form-label-enhanced">Notes</label>
                            <textarea id="form-notes" name="notes" class="user-dashboard-form-textarea-enhanced" rows="3"></textarea>
                        </div>
                        
                        <div class="user-dashboard-form-button-group-enhanced">
                            <button type="submit" class="user-dashboard-form-submit-enhanced">Submit Form</button>
                            <button type="button" class="user-dashboard-form-cancel-enhanced" onclick="resetForm()">Reset</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="test-section">
                <h2 class="test-subtitle">Test Controls</h2>
                <button class="test-button" onclick="runAllTests()">Run All Tests</button>
                <button class="test-button" onclick="toggleSidebar()">Toggle Sidebar</button>
                <button class="test-button" onclick="enableDebugMode()">Enable Debug</button>
                <button class="test-button" onclick="clearResults()">Clear Results</button>
                <button class="test-button" onclick="testMobileView()">Test Mobile View</button>
            </div>

            <!-- Test Results -->
            <div class="test-section">
                <h2 class="test-subtitle">Interaction Log</h2>
                <div id="interaction-log" style="background: #f9fafb; padding: 1rem; border-radius: 0.5rem; min-height: 100px; font-family: monospace; font-size: 0.875rem;">
                    <p>Interaction log will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the form JavaScript -->
    <script src="js/user-dashboard-forms.js"></script>
    
    <!-- Test JavaScript -->
    <script>
        let interactionCount = 0;
        
        function toggleSidebar() {
            const sidebar = document.getElementById('mockSidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('show');
            mainContent.classList.toggle('sidebar-open');
            
            logInteraction('Sidebar', sidebar.classList.contains('show') ? 'opened' : 'closed');
        }

        function enableDebugMode() {
            if (window.UserDashboardForms) {
                window.UserDashboardForms.enableDebug();
                logInteraction('Debug Mode', 'enabled');
            }
        }

        function clearResults() {
            document.getElementById('interaction-log').innerHTML = '<p>Interaction log cleared...</p>';
            interactionCount = 0;
        }

        function logInteraction(element, value) {
            interactionCount++;
            const log = document.getElementById('interaction-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${interactionCount}. ${element}: ${value}`;
            
            if (log.innerHTML.includes('Interaction log will appear here') || log.innerHTML.includes('cleared')) {
                log.innerHTML = `<p>${entry}</p>`;
            } else {
                log.innerHTML += `<p>${entry}</p>`;
            }
            
            log.scrollTop = log.scrollHeight;
        }

        function testFormSubmit(event) {
            event.preventDefault();
            logInteraction('Complete Form', 'submitted successfully');
            return false;
        }

        function resetForm() {
            document.querySelector('form').reset();
            logInteraction('Form', 'reset');
        }

        function testMobileView() {
            const viewport = document.querySelector('meta[name="viewport"]');
            if (window.innerWidth > 768) {
                alert('Resize your browser window to less than 768px to test mobile view, or use browser developer tools.');
            } else {
                logInteraction('Mobile View', 'testing on mobile viewport');
            }
        }

        function runAllTests() {
            logInteraction('Test Suite', 'started');
            
            // Test all form elements
            const elements = [
                'text-input', 'number-input', 'password-input', 
                'date-input', 'textarea-input', 'select-input'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.focus();
                    setTimeout(() => element.blur(), 100);
                    logInteraction(`${id} focus/blur`, 'tested');
                }
            });
            
            // Test z-index values
            const testElement = document.getElementById('text-input');
            const computedStyle = window.getComputedStyle(testElement);
            logInteraction('Z-index check', `input z-index: ${computedStyle.zIndex}`);
            
            const sidebar = document.getElementById('mockSidebar');
            const sidebarStyle = window.getComputedStyle(sidebar);
            logInteraction('Z-index check', `sidebar z-index: ${sidebarStyle.zIndex}`);
            
            logInteraction('Test Suite', 'completed');
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                logInteraction('Page', 'loaded successfully');
                logInteraction('Form Enhancement', 'initialized');
            }, 1000);
        });
    </script>
</body>
</html>
