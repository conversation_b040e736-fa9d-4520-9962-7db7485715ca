@extends('layouts.admin')

@section('title', 'Retur dari <PERSON> - Admin Dashboard')

@section('content')
<div class="admin-dashboard-main-content space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Retur dari <PERSON></h1>
                    <p class="text-gray-600 mt-1"><PERSON><PERSON><PERSON> permintaan retur produk dari toko-toko</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <!-- <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value">{{ number_format($stats['total'] ?? 0) }}</p>
                <p class="admin-dashboard-stat-label">Total Retur</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value">{{ number_format($stats['requested'] ?? 0) }}</p>
                <p class="admin-dashboard-stat-label">Menunggu</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value">{{ number_format($stats['approved'] ?? 0) }}</p>
                <p class="admin-dashboard-stat-label">Disetujui</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-purple">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value">{{ number_format($stats['forwarded_to_supplier'] ?? 0) }}</p>
                <p class="admin-dashboard-stat-label">Diteruskan</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value">{{ number_format($stats['completed'] ?? 0) }}</p>
                <p class="admin-dashboard-stat-label">Selesai</p>
            </div>
        </div>
    </div> -->

    <!-- Filters -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari</label>
                    <input type="text" id="search" name="search" value="{{ request('search') }}" 
                           placeholder="Cari produk atau toko..." class="admin-dashboard-input">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="admin-dashboard-select">
                        <option value="">Semua Status</option>
                        <option value="requested" {{ request('status') === 'requested' ? 'selected' : '' }}>Diminta</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Disetujui</option>
                        <option value="in_transit" {{ request('status') === 'in_transit' ? 'selected' : '' }}>Diteruskan</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Ditolak</option>
                    </select>
                </div>
                <div>
                    <label for="store_id" class="block text-sm font-medium text-gray-700 mb-1">Toko</label>
                    <select id="store_id" name="store_id" class="admin-dashboard-select">
                        <option value="">Semua Toko</option>
                        @foreach($stores as $store)
                            <option value="{{ $store->id }}" {{ request('store_id') == $store->id ? 'selected' : '' }}>
                                {{ $store->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="month" class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <select id="month" name="month" class="admin-dashboard-select">
                        @foreach($availableMonths as $month)
                            <option value="{{ $month['value'] }}" {{ ($filterMonth ?? 'all') === $month['value'] ? 'selected' : '' }}>
                                {{ $month['label'] }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary w-full">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Store Returns Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Retur dari Toko</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Toko</th>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Tanggal</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($returns as $return)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->store->name }}</div>
                                <div class="text-sm text-gray-500">{{ $return->store->location }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->product->name }}</div>
                                @if($return->supplier)
                                <div class="text-sm text-gray-500">Diteruskan ke: {{ $return->supplier->name }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ number_format($return->quantity) }}</div>
                                <div class="text-xs text-gray-500">unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600">{{ $return->reason_in_indonesian }}</div>
                                @if($return->description)
                                <div class="text-xs text-gray-500 mt-1">{{ Str::limit($return->description, 50) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->return_date->format('d M Y') }}</div>
                                <div class="text-xs text-gray-500">{{ $return->return_date->format('H:i') }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full 
                                    @if($return->status === 'requested') bg-yellow-100 text-yellow-800
                                    @elseif($return->status === 'approved') bg-blue-100 text-blue-800
                                    @elseif($return->status === 'in_transit') bg-purple-100 text-purple-800
                                    @elseif($return->status === 'completed') bg-green-100 text-green-800
                                    @elseif($return->status === 'rejected') bg-red-100 text-red-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    @if($return->status === 'requested') Diminta
                                    @elseif($return->status === 'approved') Disetujui
                                    @elseif($return->status === 'in_transit') Diteruskan
                                    @elseif($return->status === 'completed') Selesai
                                    @elseif($return->status === 'rejected') Ditolak
                                    @else {{ ucfirst($return->status ?? '') }}
                                    @endif
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.store-returns.show', $return) }}" 
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Lihat
                                    </a>
                                    @if($return->status === 'requested')
                                    <button onclick="openApproveModal('{{ $return->id }}')" 
                                            class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        Setujui
                                    </button>
                                    <button onclick="openRejectModal('{{ $return->id }}')" 
                                            class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Tolak
                                    </button>
                                    @elseif($return->status === 'approved')
                                    @if(!$return->supplier_id)
                                    <button onclick="openForwardToSupplierModal('{{ $return->id }}', '{{ $return->product->name }}', '{{ $return->product->id }}')"
                                            class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                        Teruskan ke Supplier
                                    </button>
                                    @endif
                                    <button onclick="openCompleteModal('{{ $return->id }}')" 
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Selesaikan
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum Ada Retur dari Toko</p>
                                    <p class="mb-4">Belum ada retur yang dikirim dari toko-toko</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(isset($returns) && $returns->hasPages())
            <div class="mt-6">
                {{ $returns->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Approve Modal -->
<div id="approveModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Setujui Retur dari Toko</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeApproveModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="approveForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Persetujuan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menyetujui retur dari toko ini? Setelah disetujui, Anda dapat memilih untuk meneruskannya ke supplier atau menyelesaikannya langsung.
                        </p>
                        <div>
                            <label for="approve_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Catatan (Opsional)
                            </label>
                            <textarea id="approve_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-textarea"
                                      placeholder="Tambahkan catatan untuk persetujuan..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeApproveModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Ya, Setujui Retur
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reject Modal -->
<div id="rejectModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Tolak Retur dari Toko</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeRejectModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="rejectForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penolakan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menolak retur dari toko ini? Berikan alasan penolakan yang jelas.
                        </p>
                        <div>
                            <label for="reject_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Alasan Penolakan <span class="text-red-500">*</span>
                            </label>
                            <textarea id="reject_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-textarea"
                                      placeholder="Jelaskan alasan penolakan retur..." required></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeRejectModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-danger">
                    Ya, Tolak Retur
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Forward to Supplier Modal -->
<div id="forwardToSupplierModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Teruskan ke Supplier</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeForwardToSupplierModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="forwardToSupplierForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Teruskan Retur ke Supplier</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Pilih supplier untuk meneruskan retur produk <strong id="forwardProductName"></strong> dari toko.
                        </p>
                        <div class="space-y-4">
                            <div>
                                <label for="forward_supplier_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Supplier <span class="text-red-500">*</span>
                                </label>
                                <div id="supplier_recommendation" class="mb-2 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-800" style="display: none;">
                                    <strong>💡 Rekomendasi:</strong> <span id="recommended_supplier_text"></span>
                                </div>
                                <select id="forward_supplier_id" name="supplier_id" class="admin-dashboard-select" required>
                                    <option value="">Pilih Supplier</option>
                                    @foreach(\App\Models\Supplier::active()->orderBy('name')->get() as $supplier)
                                        <option value="{{ $supplier->id }}">{{ $supplier->name }}</option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-xs text-gray-500">
                                    Anda dapat memilih supplier mana saja, namun disarankan mengembalikan ke supplier yang paling baru mengirim produk ini.
                                </p>
                            </div>
                            <div>
                                <label for="forward_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                    Catatan (Opsional)
                                </label>
                                <textarea id="forward_notes" name="admin_notes" rows="3"
                                          class="admin-dashboard-textarea"
                                          placeholder="Tambahkan catatan untuk supplier..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeForwardToSupplierModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Teruskan ke Supplier
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Complete Modal -->
<div id="completeModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Selesaikan Retur</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeCompleteModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="completeForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penyelesaian</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menyelesaikan retur ini? Stok toko akan dikurangi sesuai jumlah retur.
                        </p>
                        <div>
                            <label for="complete_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Catatan (Opsional)
                            </label>
                            <textarea id="complete_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-textarea"
                                      placeholder="Tambahkan catatan penyelesaian..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeCompleteModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Ya, Selesaikan Retur
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
// Approve Modal Functions
function openApproveModal(returnId) {
    const modal = document.getElementById('approveModal');
    const form = document.getElementById('approveForm');

    form.action = `/admin/store-returns/${returnId}/approve`;
    modal.classList.add('active');
}

function closeApproveModal() {
    const modal = document.getElementById('approveModal');
    modal.classList.remove('active');
    document.getElementById('approveForm').reset();
}

// Reject Modal Functions
function openRejectModal(returnId) {
    const modal = document.getElementById('rejectModal');
    const form = document.getElementById('rejectForm');

    form.action = `/admin/store-returns/${returnId}/reject`;
    modal.classList.add('active');
}

function closeRejectModal() {
    const modal = document.getElementById('rejectModal');
    modal.classList.remove('active');
    document.getElementById('rejectForm').reset();
}

// Forward to Supplier Modal Functions
function openForwardToSupplierModal(returnId, productName, productId) {
    const modal = document.getElementById('forwardToSupplierModal');
    const form = document.getElementById('forwardToSupplierForm');
    const productNameElement = document.getElementById('forwardProductName');
    const recommendationDiv = document.getElementById('supplier_recommendation');
    const recommendationText = document.getElementById('recommended_supplier_text');
    const supplierSelect = document.getElementById('forward_supplier_id');

    form.action = `/admin/store-returns/${returnId}/forward-to-supplier`;
    productNameElement.textContent = productName;

    // Reset supplier selection and hide recommendation
    supplierSelect.value = '';
    recommendationDiv.style.display = 'none';

    // Fetch supplier recommendation for this product
    fetch(`/admin/products/${productId}/recent-supplier`)
        .then(response => response.json())
        .then(data => {
            if (data.supplier) {
                recommendationText.textContent = `${data.supplier.name} (terakhir mengirim produk ini pada ${data.last_delivery_date})`;
                recommendationDiv.style.display = 'block';

                // Pre-select the recommended supplier
                supplierSelect.value = data.supplier.id;
            }
        })
        .catch(error => {
            console.log('Could not fetch supplier recommendation:', error);
        });

    modal.classList.add('active');
}

function closeForwardToSupplierModal() {
    const modal = document.getElementById('forwardToSupplierModal');
    modal.classList.remove('active');
    document.getElementById('forwardToSupplierForm').reset();
}

// Complete Modal Functions
function openCompleteModal(returnId) {
    const modal = document.getElementById('completeModal');
    const form = document.getElementById('completeForm');

    form.action = `/admin/store-returns/${returnId}/complete`;
    modal.classList.add('active');
}

function closeCompleteModal() {
    const modal = document.getElementById('completeModal');
    modal.classList.remove('active');
    document.getElementById('completeForm').reset();
}

// Close modals when clicking outside
document.getElementById('approveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApproveModal();
    }
});

document.getElementById('rejectModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRejectModal();
    }
});

document.getElementById('forwardToSupplierModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeForwardToSupplierModal();
    }
});

document.getElementById('completeModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCompleteModal();
    }
});
</script>
@endpush

@endsection
