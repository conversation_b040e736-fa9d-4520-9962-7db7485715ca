@extends('layouts.user')

@section('title', 'Test Mobile Interactions - Indah Berkah Abadi')

@push('styles')
<!-- Independent CSS for User Dashboard Dropdowns -->
<link rel="stylesheet" href="{{ asset('css/user-dashboard-dropdowns.css') }}">
@endpush

@section('content')
<div class="user-dashboard-container">
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Test Mobile Interactions</h1>
            <p class="user-dashboard-header-subtitle">Verify all interactive elements work on mobile devices</p>
        </div>
        <div class="user-dashboard-header-actions">
            <button class="user-dashboard-btn-primary user-dashboard-btn-sm" onclick="testHeaderButton()">
                Test Header Button
            </button>
        </div>
    </div>

    <!-- Test Form Elements -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Form Elements Test</h2>
            <p class="user-dashboard-card-description">Test all form inputs and dropdowns</p>
        </div>
        <div class="user-dashboard-card-content">
            <form class="space-y-6">
                <!-- Enhanced Select Dropdown -->
                <div class="user-dashboard-form-group-enhanced">
                    <label class="user-dashboard-form-label">Enhanced Select Dropdown</label>
                    <select class="user-dashboard-form-select-enhanced" onchange="testSelectChange(this)">
                        <option value="">Select an option...</option>
                        <option value="option1">Option 1</option>
                        <option value="option2">Option 2</option>
                        <option value="option3">Option 3</option>
                    </select>
                </div>

                <!-- Regular Form Input -->
                <div class="user-dashboard-form-group">
                    <label class="user-dashboard-form-label">Text Input</label>
                    <input type="text" class="user-dashboard-form-input" placeholder="Type something..." onchange="testInputChange(this)">
                </div>

                <!-- Textarea -->
                <div class="user-dashboard-form-group">
                    <label class="user-dashboard-form-label">Textarea</label>
                    <textarea class="user-dashboard-form-textarea" rows="3" placeholder="Enter text..." onchange="testTextareaChange(this)"></textarea>
                </div>

                <!-- Form Buttons -->
                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="button" class="user-dashboard-btn-primary user-dashboard-btn-sm" onclick="testFormButton('primary')">
                        Primary Button
                    </button>
                    <button type="button" class="user-dashboard-btn-secondary user-dashboard-btn-sm" onclick="testFormButton('secondary')">
                        Secondary Button
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Test List Items with Actions -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">List Items with Actions</h2>
            <p class="user-dashboard-card-description">Test list item action buttons</p>
        </div>
        <div class="user-dashboard-card-content">
            <div class="user-dashboard-list">
                @for($i = 1; $i <= 3; $i++)
                <div class="user-dashboard-list-item">
                    <div class="user-dashboard-list-item-content">
                        <div class="user-dashboard-list-item-main">
                            <h3 class="user-dashboard-list-item-title">Test Item {{ $i }}</h3>
                            <p class="user-dashboard-list-item-subtitle">This is a test item for mobile interaction testing</p>
                        </div>
                        <div class="user-dashboard-list-item-actions">
                            <button class="user-dashboard-btn-primary user-dashboard-btn-sm" onclick="testListAction('view', {{ $i }})">
                                View
                            </button>
                            <button class="user-dashboard-btn-secondary user-dashboard-btn-sm" onclick="testListAction('edit', {{ $i }})">
                                Edit
                            </button>
                        </div>
                    </div>
                </div>
                @endfor
            </div>
        </div>
    </div>

    <!-- Test Time Period Filter -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Time Period Filter Test</h2>
        </div>
        <div class="user-dashboard-card-content">
            <div class="flex flex-wrap gap-2">
                <button class="user-dashboard-time-period-btn user-dashboard-time-period-btn-active" onclick="testTimePeriod('day')">
                    Today
                </button>
                <button class="user-dashboard-time-period-btn user-dashboard-time-period-btn-inactive" onclick="testTimePeriod('week')">
                    This Week
                </button>
                <button class="user-dashboard-time-period-btn user-dashboard-time-period-btn-inactive" onclick="testTimePeriod('month')">
                    This Month
                </button>
                <button class="user-dashboard-time-period-btn user-dashboard-time-period-btn-inactive" onclick="testTimePeriod('all')">
                    All Time
                </button>
            </div>
        </div>
    </div>

    <!-- Test Results Display -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Test Results</h2>
        </div>
        <div class="user-dashboard-card-content">
            <div id="test-results" class="space-y-2">
                <p class="text-gray-600">Click on elements above to test their functionality...</p>
            </div>
            <div class="mt-4">
                <button class="user-dashboard-btn-secondary user-dashboard-btn-sm" onclick="clearTestResults()">
                    Clear Results
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Device Info -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Device Information</h2>
        </div>
        <div class="user-dashboard-card-content">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                    <label class="user-dashboard-form-label">Screen Width</label>
                    <p id="screen-width" class="text-gray-900">-</p>
                </div>
                <div>
                    <label class="user-dashboard-form-label">Screen Height</label>
                    <p id="screen-height" class="text-gray-900">-</p>
                </div>
                <div>
                    <label class="user-dashboard-form-label">Device Type</label>
                    <p id="device-type" class="text-gray-900">-</p>
                </div>
                <div>
                    <label class="user-dashboard-form-label">Touch Support</label>
                    <p id="touch-support" class="text-gray-900">-</p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<!-- Independent JavaScript for User Dashboard Dropdowns -->
<script src="{{ asset('js/user-dashboard-dropdowns.js') }}"></script>

<script>
// Test functions
function addTestResult(message, type = 'info') {
    const resultsDiv = document.getElementById('test-results');
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = type === 'success' ? 'text-green-600' : type === 'error' ? 'text-red-600' : 'text-blue-600';
    
    const resultElement = document.createElement('div');
    resultElement.className = `p-2 bg-gray-50 rounded text-sm ${colorClass}`;
    resultElement.innerHTML = `<span class="font-mono text-xs text-gray-500">[${timestamp}]</span> ${message}`;
    
    resultsDiv.appendChild(resultElement);
    resultElement.scrollIntoView({ behavior: 'smooth' });
}

function testHeaderButton() {
    addTestResult('Header button clicked successfully!', 'success');
}

function testSelectChange(select) {
    addTestResult(`Select dropdown changed to: ${select.value}`, 'success');
}

function testInputChange(input) {
    addTestResult(`Text input changed to: ${input.value}`, 'success');
}

function testTextareaChange(textarea) {
    addTestResult(`Textarea changed to: ${textarea.value}`, 'success');
}

function testFormButton(type) {
    addTestResult(`${type.charAt(0).toUpperCase() + type.slice(1)} form button clicked!`, 'success');
}

function testListAction(action, itemId) {
    addTestResult(`List item ${itemId} ${action} button clicked!`, 'success');
}

function testTimePeriod(period) {
    // Update button states
    document.querySelectorAll('.user-dashboard-time-period-btn').forEach(btn => {
        btn.className = btn.className.replace('user-dashboard-time-period-btn-active', 'user-dashboard-time-period-btn-inactive');
    });
    event.target.className = event.target.className.replace('user-dashboard-time-period-btn-inactive', 'user-dashboard-time-period-btn-active');
    
    addTestResult(`Time period changed to: ${period}`, 'success');
}

function clearTestResults() {
    const resultsDiv = document.getElementById('test-results');
    resultsDiv.innerHTML = '<p class="text-gray-600">Test results cleared...</p>';
}

// Update device information
function updateDeviceInfo() {
    document.getElementById('screen-width').textContent = window.innerWidth + 'px';
    document.getElementById('screen-height').textContent = window.innerHeight + 'px';
    
    let deviceType = 'Desktop';
    if (window.innerWidth <= 480) deviceType = 'Small Mobile';
    else if (window.innerWidth <= 768) deviceType = 'Mobile';
    else if (window.innerWidth <= 1024) deviceType = 'Tablet';
    
    document.getElementById('device-type').textContent = deviceType;
    document.getElementById('touch-support').textContent = 'ontouchstart' in window ? 'Yes' : 'No';
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateDeviceInfo();
    addTestResult('Mobile interaction test page loaded successfully!', 'success');
    
    // Update device info on resize
    window.addEventListener('resize', updateDeviceInfo);
});
</script>
@endpush

@endsection
