@extends('layouts.app')

@section('title', 'Indah Berkah Abadi - Sistem Inventori Terpadu')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/homepage.css') }}">
@endpush

@section('content')
<div class="iba-homepage-container">
    <!-- Dynamic Header -->
    <header class="iba-homepage-header">
        <div class="iba-homepage-header-container">
            <div class="iba-homepage-logo-section">
                <div class="iba-homepage-logo">
                    <span class="iba-homepage-logo-text">IBA</span>
                </div>
                <h1 class="iba-homepage-company-name">Indah Berkah Abadi</h1>
            </div>
            
            <div class="iba-homepage-header-actions">
                @auth
                    <!-- Authenticated User Display -->
                    <div class="iba-homepage-user-info">
                        <div class="iba-homepage-user-avatar">
                            {{ substr(auth()->user()->name, 0, 1) }}
                        </div>
                        <div class="iba-homepage-user-details">
                            <h3>{{ auth()->user()->name }}</h3>
                            <p>{{ auth()->user()->isAdmin() ? 'Administrator' : 'Pengguna Toko' }}</p>
                        </div>
                    </div>
                    
                    @if(auth()->user()->isAdmin())
                        <a href="{{ route('admin.dashboard') }}" class="iba-homepage-btn iba-homepage-btn-primary">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Dashboard Admin
                        </a>
                    @else
                        <a href="{{ route('user.dashboard') }}" class="iba-homepage-btn iba-homepage-btn-primary">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Dashboard Toko
                        </a>
                    @endif
                    
                    <form method="POST" action="{{ route('logout') }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="iba-homepage-btn iba-homepage-btn-secondary">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            Keluar
                        </button>
                    </form>
                @else
                    <!-- Guest User Display -->
                    <a href="{{ route('login') }}" class="iba-homepage-btn iba-homepage-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1"></path>
                        </svg>
                        Masuk Sistem
                    </a>
                @endauth
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="iba-homepage-hero">
        <div class="iba-homepage-hero-container">
            <div class="iba-homepage-hero-badge">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Sistem Manajemen Logistik & Inventori Terpadu
            </div>

            <h2 class="iba-homepage-hero-title">
                Solusi Inventori<br>
                <span style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Masa Depan</span>
            </h2>

            <p class="iba-homepage-hero-subtitle">
                Platform manajemen inventori terdepan untuk mengelola <strong>{{ $stats['stores'] }}+ lokasi toko</strong> dengan teknologi real-time tracking dan analitik mendalam. Tingkatkan efisiensi operasional dan kontrol inventori Anda.
            </p>

            <div class="iba-homepage-hero-actions">
                @auth
                    @if(auth()->user()->isAdmin())
                        <a href="{{ route('admin.dashboard') }}" class="iba-homepage-btn iba-homepage-btn-primary" style="padding: 1rem 2.5rem; font-size: 1rem;">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Kelola Sistem
                        </a>
                    @else
                        <a href="{{ route('user.dashboard') }}" class="iba-homepage-btn iba-homepage-btn-primary" style="padding: 1rem 2.5rem; font-size: 1rem;">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Dashboard Toko
                        </a>
                    @endif
                @else
                    <a href="{{ route('login') }}" class="iba-homepage-btn iba-homepage-btn-primary" style="padding: 1rem 2.5rem; font-size: 1rem;">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013 3v1"></path>
                        </svg>
                        Mulai Sekarang
                    </a>
                @endauth
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="iba-homepage-stats">
        <div class="iba-homepage-stats-container">
            <div class="iba-homepage-stats-grid">
                <div class="iba-homepage-stats-card">
                    <div class="iba-homepage-stats-number" style="color: #2563eb;">{{ $stats['warehouse'] }}</div>
                    <div class="iba-homepage-stats-label">Gudang Pusat</div>
                </div>
                <div class="iba-homepage-stats-card">
                    <div class="iba-homepage-stats-number" style="color: #059669;">{{ $stats['stores'] }}+</div>
                    <div class="iba-homepage-stats-label">Lokasi Toko</div>
                </div>
                <div class="iba-homepage-stats-card">
                    <div class="iba-homepage-stats-number" style="color: #d97706;">{{ $stats['realtime_tracking'] }}%</div>
                    <div class="iba-homepage-stats-label">Akurasi Real-time</div>
                </div>
                <div class="iba-homepage-stats-card">
                    <div class="iba-homepage-stats-number" style="color: #7c3aed;">{{ $stats['availability'] }}/7</div>
                    <div class="iba-homepage-stats-label">Ketersediaan Sistem</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="iba-homepage-features">
        <div class="iba-homepage-features-container">
            <div class="iba-homepage-features-header">
                <h3 class="iba-homepage-features-title">Fitur Unggulan</h3>
                <p class="iba-homepage-features-subtitle">
                    Teknologi terdepan untuk mengelola seluruh rantai pasokan dengan efisiensi maksimal
                </p>
            </div>

            <div class="iba-homepage-features-grid">
                @foreach($features as $index => $feature)
                <div class="iba-homepage-feature-card">
                    <div class="iba-homepage-feature-icon">
                        @if($feature['icon'] == 'warehouse')
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        @elseif($feature['icon'] == 'store')
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        @elseif($feature['icon'] == 'truck')
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        @elseif($feature['icon'] == 'chart')
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        @elseif($feature['icon'] == 'shield')
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        @else
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        @endif
                    </div>
                    <h4 class="iba-homepage-feature-title">{{ $feature['title'] }}</h4>
                    <p class="iba-homepage-feature-description">{{ $feature['description'] }}</p>
                    <ul class="iba-homepage-feature-list">
                        @foreach($feature['items'] as $item)
                        <li class="iba-homepage-feature-item">
                            <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ $item }}
                        </li>
                        @endforeach
                    </ul>
                </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="iba-homepage-footer">
        <div class="iba-homepage-footer-container">
            <div class="iba-homepage-footer-logo-section">
                <div class="iba-homepage-logo">
                    <span class="iba-homepage-logo-text">IBA</span>
                </div>
                <span class="iba-homepage-footer-title">Indah Berkah Abadi</span>
            </div>
            <p class="iba-homepage-footer-description">
                Solusi manajemen inventori terdepan untuk bisnis modern dengan teknologi dan inovasi terkini.
            </p>
            <div class="iba-homepage-footer-divider">
                <p class="iba-homepage-footer-copyright">&copy; 2025 Indah Berkah Abadi. Hak cipta dilindungi undang-undang.</p>
            </div>
        </div>
    </footer>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/homepage.js') }}"></script>
@endpush
