<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ke<PERSON><PERSON> ke Dashboard - PT. Indah Berkah <PERSON>i</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%23166534'/><text x='50' y='65' font-family='Arial,sans-serif' font-size='36' font-weight='bold' text-anchor='middle' fill='white'>IBA</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1a202c;
            line-height: 1.6;
        }

        .navigation-container {
            max-width: 600px;
            width: 90%;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            text-align: center;
        }

        .navigation-header {
            background: linear-gradient(135deg, #166534 0%, #15803d 100%);
            color: white;
            padding: 2rem;
        }

        .company-logo {
            width: 64px;
            height: 64px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 24px;
            font-weight: bold;
        }

        .navigation-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .navigation-subtitle {
            font-size: 1rem;
            opacity: 0.9;
        }

        .navigation-content {
            padding: 2rem;
        }

        .welcome-message {
            margin-bottom: 2rem;
        }

        .welcome-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
        }

        .welcome-description {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .dashboard-action {
            display: inline-block;
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #166534 0%, #15803d 100%);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.125rem;
            transition: all 0.2s ease;
            margin-bottom: 1.5rem;
        }

        .dashboard-action:hover {
            background: linear-gradient(135deg, #14532d 0%, #166534 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(22, 101, 52, 0.3);
        }

        .dashboard-action svg {
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
            vertical-align: middle;
        }

        .store-info {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .store-info-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #166534;
            margin-bottom: 0.5rem;
        }

        .store-info-details {
            font-size: 0.875rem;
            color: #15803d;
        }

        .navigation-footer {
            background: #f9fafb;
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            color: #6b7280;
            text-decoration: none;
            font-size: 0.875rem;
            transition: color 0.2s ease;
        }

        .back-link:hover {
            color: #166534;
        }

        .back-link svg {
            width: 16px;
            height: 16px;
            margin-right: 0.5rem;
        }

        @media (max-width: 480px) {
            .navigation-container {
                width: 95%;
                margin: 1rem;
            }
            
            .navigation-header {
                padding: 1.5rem;
            }
            
            .navigation-content {
                padding: 1.5rem;
            }
            
            .dashboard-action {
                padding: 1.25rem 1.5rem;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="navigation-container">
        <div class="navigation-header">
            <div class="company-logo">IBA</div>
            <h1 class="navigation-title">PT. Indah Berkah Abadi</h1>
            <p class="navigation-subtitle">Sistem Inventori</p>
        </div>
        
        <div class="navigation-content">
            <div class="welcome-message">
                <h2 class="welcome-title">Selamat Datang, {{ auth()->user()->name }}</h2>
                <p class="welcome-description">
                    Kembali ke dashboard toko Anda untuk melanjutkan pengelolaan inventori dan operasi harian.
                </p>
            </div>
            
            @if(auth()->user()->store)
            <div class="store-info">
                <div class="store-info-title">Informasi Toko Anda</div>
                <div class="store-info-details">
                    <strong>{{ auth()->user()->store->name }}</strong><br>
                    {{ auth()->user()->store->location }}
                </div>
            </div>
            @endif
            
            <a href="{{ route('user.dashboard') }}" class="dashboard-action">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                Kembali ke Dashboard Toko
            </a>
        </div>
        
        <div class="navigation-footer">
            <a href="{{ route('home') }}" class="back-link">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali ke Beranda
            </a>
        </div>
    </div>
</body>
</html>
