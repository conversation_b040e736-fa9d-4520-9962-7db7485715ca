/**
 * Dedicated JavaScript for User Dashboard Sidebar Dropdown Fix
 * PT. Indah Berkah Abadi - Inventory System
 * 
 * This script specifically handles the sidebar dropdown functionality
 * and prevents conflicts with other JavaScript files.
 */

(function() {
    'use strict';

    // Debug logging
    const DEBUG = true;
    function log(message, data = null) {
        if (DEBUG) {
            console.log('[SidebarFix]', message, data || '');
        }
    }

    // Global state
    let isDropdownOpen = false;
    let userMenuButton = null;
    let userMenuDropdown = null;
    let initializationAttempts = 0;
    let lastClickTime = 0;
    let isInitializing = false;
    const MAX_INIT_ATTEMPTS = 10;
    const CLICK_DEBOUNCE_TIME = 100; // Prevent rapid clicks

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeSidebarDropdown);
    } else {
        initializeSidebarDropdown();
    }

    function initializeSidebarDropdown() {
        isInitializing = true;
        initializationAttempts++;
        log(`Initializing sidebar dropdown (attempt ${initializationAttempts})`);

        // Find elements
        userMenuButton = document.getElementById('userMenuButton');
        userMenuDropdown = document.getElementById('userMenuDropdown');

        if (!userMenuButton || !userMenuDropdown) {
            log('Elements not found, retrying...', {
                button: !!userMenuButton,
                dropdown: !!userMenuDropdown
            });

            isInitializing = false;
            if (initializationAttempts < MAX_INIT_ATTEMPTS) {
                setTimeout(initializeSidebarDropdown, 200);
            } else {
                console.error('[SidebarFix] Failed to find sidebar elements after maximum attempts');
            }
            return;
        }

        log('✅ Sidebar elements found successfully');

        // Remove any existing event listeners
        cleanupEventListeners();

        // Disable any other dropdown functionality that might conflict
        disableConflictingScripts();

        // Setup new event listeners
        setupEventListeners();

        // Initialize dropdown state
        closeDropdown();

        // Make functions globally available
        window.sidebarDropdownToggle = toggleDropdown;
        window.sidebarDropdownClose = closeDropdown;
        window.sidebarDropdownOpen = openDropdown;

        // Override any other dropdown functions
        window.toggleUserMenu = toggleDropdown;
        window.closeUserMenu = closeDropdown;
        window.openUserMenu = openDropdown;

        // Mark initialization as complete
        setTimeout(() => {
            isInitializing = false;
            log('🎉 Sidebar dropdown initialization complete');
        }, 500);

        log('🎉 Sidebar dropdown initialized successfully');
    }

    function disableConflictingScripts() {
        // Remove any existing click handlers on the button
        const newButton = userMenuButton.cloneNode(true);
        userMenuButton.parentNode.replaceChild(newButton, userMenuButton);
        userMenuButton = newButton;

        // Clear any existing global functions that might conflict
        if (window.toggleUserMenu && window.toggleUserMenu !== toggleDropdown) {
            log('🚫 Disabling conflicting toggleUserMenu function');
            delete window.toggleUserMenu;
        }
        if (window.closeUserMenu && window.closeUserMenu !== closeDropdown) {
            log('🚫 Disabling conflicting closeUserMenu function');
            delete window.closeUserMenu;
        }
    }

    function cleanupEventListeners() {
        // This is now handled in disableConflictingScripts
        log('🧹 Event listeners cleanup handled by disableConflictingScripts');
    }

    function setupEventListeners() {
        // Primary click handler - use only one event type to avoid conflicts
        userMenuButton.addEventListener('click', handleButtonClick, {
            capture: false,
            passive: false
        });

        // Keyboard support
        userMenuButton.addEventListener('keydown', handleKeydown);

        // Outside click detection - delay to prevent immediate closure
        setTimeout(() => {
            document.addEventListener('click', handleDocumentClick, {
                capture: false,
                passive: true
            });
        }, 300); // Increased delay to prevent immediate closure

        // Escape key support
        document.addEventListener('keydown', handleEscapeKey);

        log('Event listeners setup complete');
    }

    function handleButtonClick(e) {
        const currentTime = Date.now();

        // Debounce rapid clicks
        if (currentTime - lastClickTime < CLICK_DEBOUNCE_TIME) {
            log('🚫 Click debounced - too rapid');
            return;
        }
        lastClickTime = currentTime;

        log('🖱️ Button click detected', e.type);

        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();

        // Force focus for accessibility
        userMenuButton.focus();

        // Add a small delay to ensure all other event handlers have finished
        setTimeout(() => {
            toggleDropdown();
        }, 10);
    }

    function handleKeydown(e) {
        log('⌨️ Key pressed on button:', e.key);

        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown();
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            openDropdown();
            focusFirstDropdownItem();
        } else if (e.key === 'Escape') {
            closeDropdown();
        }
    }

    function handleDocumentClick(e) {
        // Don't process clicks immediately after opening or during initialization
        if (!isDropdownOpen || isInitializing) {
            return;
        }

        // Don't process clicks too soon after the last button click
        const timeSinceLastClick = Date.now() - lastClickTime;
        if (timeSinceLastClick < 200) {
            log('📍 Ignoring document click - too soon after button click');
            return;
        }

        // Check if click is outside the dropdown area
        const isInsideButton = userMenuButton && userMenuButton.contains(e.target);
        const isInsideDropdown = userMenuDropdown && userMenuDropdown.contains(e.target);

        // Additional check for any sidebar-related elements
        const isInsideSidebar = e.target.closest('.sidebar, .user-dashboard-user-menu');

        if (!isInsideButton && !isInsideDropdown && !isInsideSidebar) {
            log('📍 Outside click detected, closing dropdown');
            closeDropdown();
        } else {
            log('📍 Click inside dropdown area, keeping open');
        }
    }

    function handleEscapeKey(e) {
        if (e.key === 'Escape' && isDropdownOpen) {
            log('⎋ Escape key pressed, closing dropdown');
            closeDropdown();
        }
    }

    function toggleDropdown() {
        if (isDropdownOpen) {
            closeDropdown();
        } else {
            openDropdown();
        }
    }

    function openDropdown() {
        if (!userMenuDropdown) return;

        log('📂 Opening dropdown');

        // Force the dropdown to be visible
        userMenuDropdown.style.setProperty('display', 'block', 'important');
        userMenuDropdown.style.setProperty('visibility', 'visible', 'important');
        userMenuDropdown.style.setProperty('opacity', '1', 'important');
        userMenuDropdown.style.setProperty('pointer-events', 'auto', 'important');

        userMenuButton.setAttribute('aria-expanded', 'true');
        userMenuButton.classList.add('active');

        isDropdownOpen = true;

        log('✅ Dropdown opened successfully');
    }

    function closeDropdown() {
        if (!userMenuDropdown) return;

        log('📁 Closing dropdown');

        // Force the dropdown to be hidden
        userMenuDropdown.style.setProperty('display', 'none', 'important');
        userMenuDropdown.style.setProperty('visibility', 'hidden', 'important');
        userMenuDropdown.style.setProperty('opacity', '0', 'important');
        userMenuDropdown.style.setProperty('pointer-events', 'none', 'important');

        userMenuButton.setAttribute('aria-expanded', 'false');
        userMenuButton.classList.remove('active');

        isDropdownOpen = false;

        log('✅ Dropdown closed successfully');
    }

    function focusFirstDropdownItem() {
        if (!userMenuDropdown) return;

        const firstItem = userMenuDropdown.querySelector('.user-dashboard-dropdown-item');
        if (firstItem) {
            firstItem.focus();
            log('🎯 Focused first dropdown item');
        }
    }

    // Error handling wrapper
    function safeExecute(fn, context = 'Unknown') {
        try {
            return fn();
        } catch (error) {
            console.error(`[SidebarFix] Error in ${context}:`, error);
            return null;
        }
    }

    // Enhanced debug functions with error handling
    window.sidebarDropdownDebug = {
        getState: () => safeExecute(() => ({
            isOpen: isDropdownOpen,
            hasButton: !!userMenuButton,
            hasDropdown: !!userMenuDropdown,
            attempts: initializationAttempts,
            lastClickTime: lastClickTime,
            isInitializing: isInitializing
        }), 'getState'),

        reinitialize: () => safeExecute(initializeSidebarDropdown, 'reinitialize'),
        forceOpen: () => safeExecute(openDropdown, 'forceOpen'),
        forceClose: () => safeExecute(closeDropdown, 'forceClose'),

        testDropdown: () => safeExecute(() => {
            log('🧪 Testing dropdown functionality');
            if (!userMenuButton || !userMenuDropdown) {
                log('❌ Elements not found for testing');
                return false;
            }

            // Test open
            openDropdown();
            setTimeout(() => {
                if (isDropdownOpen) {
                    log('✅ Open test passed');
                    // Test close
                    closeDropdown();
                    setTimeout(() => {
                        if (!isDropdownOpen) {
                            log('✅ Close test passed');
                            log('🎉 All dropdown tests passed');
                        } else {
                            log('❌ Close test failed');
                        }
                    }, 100);
                } else {
                    log('❌ Open test failed');
                }
            }, 100);
            return true;
        }, 'testDropdown')
    };

    // Global error handler for unhandled errors
    window.addEventListener('error', function(e) {
        if (e.message && e.message.includes('sidebar')) {
            console.error('[SidebarFix] Unhandled error:', e.error);
        }
    });

    log('Sidebar dropdown fix script loaded with error handling');
})();
