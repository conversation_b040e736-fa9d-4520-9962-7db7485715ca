@extends('layouts.supplier')

@section('title', 'Riwayat Retur')

@section('content')
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Riwayat Retur Produk</h1>
                    <p class="text-gray-600 mt-1">Riwayat lengkap semua retur produk di seluruh rantai pasokan</p>
                    <p class="text-sm text-gray-500 mt-1">
                        <span class="inline-flex items-center">
                            <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Menampilkan semua retur (toko→gudang→supplier) untuk visibilitas menyeluruh
                        </span>
                    </p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('supplier.return-history.export', request()->query()) }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Export CSV
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <!-- <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-content">
                <div class="supplier-dashboard-stat-icon supplier-dashboard-stat-icon-blue">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div>
                    <p class="supplier-dashboard-stat-label">Total Retur</p>
                    <p class="supplier-dashboard-stat-value">{{ number_format($stats['total_returns']) }}</p>
                </div>
            </div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-content">
                <div class="supplier-dashboard-stat-icon supplier-dashboard-stat-icon-orange">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="supplier-dashboard-stat-label">Diminta</p>
                    <p class="supplier-dashboard-stat-value">{{ number_format($stats['requested']) }}</p>
                </div>
            </div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-content">
                <div class="supplier-dashboard-stat-icon supplier-dashboard-stat-icon-green">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div>
                    <p class="supplier-dashboard-stat-label">Disetujui</p>
                    <p class="supplier-dashboard-stat-value">{{ number_format($stats['approved']) }}</p>
                </div>
            </div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-content">
                <div class="supplier-dashboard-stat-icon supplier-dashboard-stat-icon-blue">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="supplier-dashboard-stat-label">Selesai</p>
                    <p class="supplier-dashboard-stat-value">{{ number_format($stats['completed']) }}</p>
                </div>
            </div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-content">
                <div class="supplier-dashboard-stat-icon supplier-dashboard-stat-icon-red">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <div>
                    <p class="supplier-dashboard-stat-label">Ditolak</p>
                    <p class="supplier-dashboard-stat-value">{{ number_format($stats['rejected']) }}</p>
                </div>
            </div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-content">
                <div class="supplier-dashboard-stat-icon supplier-dashboard-stat-icon-gray">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                    </svg>
                </div>
                <div>
                    <p class="supplier-dashboard-stat-label">Diproses</p>
                    <p class="supplier-dashboard-stat-value">{{ number_format($stats['processed']) }}</p>
                </div>
            </div>
        </div>
    </div> -->

    <!-- Filters -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <form method="GET" action="{{ route('supplier.return-history.index') }}" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Month Filter -->
                    <div>
                        <label for="month" class="block text-sm font-medium text-gray-700 mb-2">Bulan</label>
                        <select name="month" id="month" class="supplier-dashboard-select">
                            @foreach($availableMonths as $monthOption)
                                <option value="{{ $monthOption['value'] }}" {{ $filterMonth === $monthOption['value'] ? 'selected' : '' }}>
                                    {{ $monthOption['label'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Cari</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}" 
                               placeholder="Cari produk, toko..." class="supplier-dashboard-input">
                    </div>

                    <!-- Filter Button -->
                    <div class="flex items-end">
                        <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary w-full">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns History Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Riwayat Retur Produk</h2>
            <p class="text-sm text-gray-600 mt-1">Daftar lengkap semua retur produk di seluruh rantai pasokan</p>
        </div>
        <div class="supplier-dashboard-card-content">
            @if($returns->count() > 0)
            <div class="overflow-x-auto">
                <table class="supplier-dashboard-table">
                    <thead>
                        <tr>
                            <th>Tanggal Retur</th>
                            <th>Jenis Retur</th>
                            <th>Produk</th>
                            <th>Asal</th>
                            <th>Jumlah</th>
                            <th>Alasan</th>
                            <th>Status</th>
                            <th>Catatan</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($returns as $return)
                        <tr>
                            <td>
                                <div class="font-medium text-gray-900">
                                    {{ $return->return_date->format('d/m/Y') }}
                                </div>
                                @if($return->processed_date)
                                <div class="text-xs text-gray-500">
                                    Diproses: {{ $return->processed_date->format('d/m/Y') }}
                                </div>
                                @endif
                            </td>
                            <td>
                                @if($return->processing_action === 'cancelled_delivery_approved')
                                    <span class="supplier-dashboard-badge supplier-dashboard-badge-orange">
                                        Pengiriman Dibatalkan
                                    </span>
                                @else
                                    <span class="supplier-dashboard-badge
                                        @if($return->store_id) supplier-dashboard-badge-blue
                                        @else supplier-dashboard-badge-purple
                                        @endif">
                                        {{ $return->store_id ? 'Retur dari Toko' : 'Retur dari Gudang' }}
                                    </span>
                                @endif
                            </td>
                            <td>
                                <div class="font-medium text-gray-900">{{ $return->product->name }}</div>
                                <div class="text-sm text-gray-500">{{ $return->product->sku }}</div>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    {{ $return->store->name ?? 'Gudang Pusat' }}
                                </div>
                            </td>
                            <td>
                                <span class="supplier-dashboard-badge supplier-dashboard-badge-blue">
                                    {{ number_format($return->quantity) }} unit
                                </span>
                            </td>
                            <td>
                                <span class="supplier-dashboard-badge supplier-dashboard-badge-gray">
                                    {{ $return->reason_in_indonesian }}
                                </span>
                            </td>
                            <td>
                                <span class="supplier-dashboard-badge
                                    @if($return->status === 'requested') supplier-dashboard-badge-orange
                                    @elseif($return->status === 'approved') supplier-dashboard-badge-green
                                    @elseif($return->status === 'completed') supplier-dashboard-badge-blue
                                    @elseif($return->status === 'rejected') supplier-dashboard-badge-red
                                    @elseif($return->status === 'processed') supplier-dashboard-badge-gray
                                    @else supplier-dashboard-badge-gray
                                    @endif">
                                    @if($return->status === 'requested') Diminta
                                    @elseif($return->status === 'approved') Disetujui
                                    @elseif($return->status === 'completed') Selesai
                                    @elseif($return->status === 'rejected') Ditolak
                                    @elseif($return->status === 'processed') Diproses
                                    @else {{ ucfirst($return->status) }}
                                    @endif
                                </span>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900 max-w-xs truncate" title="{{ $return->admin_notes ?? $return->processing_notes }}">
                                    {{ $return->admin_notes ?? $return->processing_notes ?? '-' }}
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $returns->links() }}
            </div>
            @else
            <div class="text-center py-12">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak Ada Riwayat Retur</h3>
                <p class="text-gray-500">Belum ada retur yang diproses untuk periode yang dipilih.</p>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
