/**
 * PT. Indah Berkah Abadi - Analytics Sidebar JavaScript
 * Distribution Comparison Analytics Interactive Features
 * 
 * Features:
 * - Sidebar toggle functionality
 * - Filter form handling
 * - Clickable analytics items
 * - Distribution detail modals
 * - Mobile-friendly interactions
 * - Proper z-index hierarchy management
 */

// Global state management
let analyticsState = {
    adminSidebarOpen: false,
    userSidebarOpen: false,
    currentDetailModal: null
};

// ===== ADMIN ANALYTICS SIDEBAR FUNCTIONS =====

/**
 * Toggle admin analytics sidebar
 */
function toggleAnalyticsSidebar() {
    const sidebar = document.querySelector('.admin-dashboard-analytics-sidebar');
    const overlay = document.querySelector('.admin-dashboard-analytics-overlay');
    const toggleBtn = document.querySelector('.admin-dashboard-analytics-toggle-btn');
    
    if (!sidebar || !overlay) return;
    
    analyticsState.adminSidebarOpen = !analyticsState.adminSidebarOpen;
    
    if (analyticsState.adminSidebarOpen) {
        sidebar.classList.add('active');
        overlay.classList.add('active');
        toggleBtn.style.display = 'none';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling on mobile
    } else {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
        toggleBtn.style.display = 'flex';
        document.body.style.overflow = '';
    }
}

/**
 * Handle admin analytics filter form submission
 */
function handleAdminAnalyticsFilter(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    // Build query parameters
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            params.append(key, value);
        }
    }
    
    // Reload page with filters
    const currentUrl = new URL(window.location);
    const newUrl = `${currentUrl.pathname}?${params.toString()}`;
    
    // Show loading state
    showAnalyticsLoading();
    
    // Navigate to filtered results
    window.location.href = newUrl;
}

/**
 * Show distribution detail modal (admin)
 */
function showDistributionDetail(distributionId) {
    if (!distributionId) return;
    
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'analytics-detail-modal-overlay';
    modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1100;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
    `;
    
    // Create modal content
    const modalContent = document.createElement('div');
    modalContent.className = 'analytics-detail-modal-content';
    modalContent.style.cssText = `
        background: white;
        border-radius: 12px;
        max-width: 500px;
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    `;
    
    // Modal header
    const modalHeader = document.createElement('div');
    modalHeader.style.cssText = `
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        justify-content: space-between;
        align-items: center;
    `;
    
    modalHeader.innerHTML = `
        <h3 style="font-size: 1.125rem; font-weight: 600; color: #1f2937; margin: 0;">
            Detail Distribusi
        </h3>
        <button onclick="closeDistributionDetail()" style="
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
        ">
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;
    
    // Modal body with loading state
    const modalBody = document.createElement('div');
    modalBody.style.cssText = `
        padding: 1.5rem;
        text-align: center;
        color: #6b7280;
    `;
    modalBody.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="animation: spin 1s linear infinite;">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Memuat detail distribusi...
        </div>
    `;
    
    // Assemble modal
    modalContent.appendChild(modalHeader);
    modalContent.appendChild(modalBody);
    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);
    
    // Store reference for cleanup
    analyticsState.currentDetailModal = modalOverlay;
    
    // Fetch distribution details (simulate API call)
    setTimeout(() => {
        loadDistributionDetails(distributionId, modalBody);
    }, 500);
    
    // Close on overlay click
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            closeDistributionDetail();
        }
    });
}

/**
 * Load distribution details into modal
 */
function loadDistributionDetails(distributionId, container) {
    // Simulate API response (in real implementation, this would be an AJAX call)
    const mockData = {
        id: distributionId,
        product_name: 'Produk Sample',
        store_name: 'Toko Sample',
        quantity_sent: 100,
        quantity_received: 95,
        date_distributed: '2024-01-15',
        notes: 'Kekurangan 5 unit karena kerusakan dalam perjalanan',
        status: 'confirmed'
    };
    
    const difference = mockData.quantity_received - mockData.quantity_sent;
    const discrepancyType = difference < 0 ? 'shortage' : (difference > 0 ? 'overage' : 'perfect');
    const discrepancyColor = discrepancyType === 'shortage' ? '#dc2626' : 
                            (discrepancyType === 'overage' ? '#d97706' : '#059669');
    
    container.innerHTML = `
        <div style="text-align: left;">
            <div style="margin-bottom: 1rem;">
                <label style="font-size: 0.875rem; font-weight: 500; color: #6b7280;">Produk</label>
                <div style="font-weight: 600; color: #1f2937;">${mockData.product_name}</div>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label style="font-size: 0.875rem; font-weight: 500; color: #6b7280;">Toko Tujuan</label>
                <div style="font-weight: 600; color: #1f2937;">${mockData.store_name}</div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-bottom: 1rem;">
                <div>
                    <label style="font-size: 0.875rem; font-weight: 500; color: #6b7280;">Jumlah Dikirim</label>
                    <div style="font-size: 1.25rem; font-weight: 700; color: #1f2937;">${mockData.quantity_sent}</div>
                </div>
                <div>
                    <label style="font-size: 0.875rem; font-weight: 500; color: #6b7280;">Jumlah Diterima</label>
                    <div style="font-size: 1.25rem; font-weight: 700; color: ${discrepancyColor};">${mockData.quantity_received}</div>
                </div>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label style="font-size: 0.875rem; font-weight: 500; color: #6b7280;">Selisih</label>
                <div style="
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 0.75rem;
                    border-radius: 6px;
                    font-weight: 600;
                    background: ${discrepancyType === 'shortage' ? '#fef2f2' : 
                                (discrepancyType === 'overage' ? '#fefce8' : '#dcfce7')};
                    color: ${discrepancyColor};
                ">
                    ${difference === 0 ? 'Sempurna' : `${difference > 0 ? '+' : ''}${difference} unit`}
                </div>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label style="font-size: 0.875rem; font-weight: 500; color: #6b7280;">Tanggal Distribusi</label>
                <div style="color: #1f2937;">${new Date(mockData.date_distributed).toLocaleDateString('id-ID')}</div>
            </div>
            
            ${mockData.notes ? `
                <div style="margin-bottom: 1rem;">
                    <label style="font-size: 0.875rem; font-weight: 500; color: #6b7280;">Catatan</label>
                    <div style="
                        padding: 0.75rem;
                        background: #f9fafb;
                        border-radius: 6px;
                        color: #374151;
                        font-style: italic;
                    ">${mockData.notes}</div>
                </div>
            ` : ''}
        </div>
    `;
}

/**
 * Close distribution detail modal
 */
function closeDistributionDetail() {
    if (analyticsState.currentDetailModal) {
        document.body.removeChild(analyticsState.currentDetailModal);
        analyticsState.currentDetailModal = null;
    }
}

// ===== USER ANALYTICS SIDEBAR FUNCTIONS =====

/**
 * Toggle user analytics sidebar
 */
function toggleUserAnalyticsSidebar() {
    const sidebar = document.querySelector('.user-dashboard-analytics-sidebar');
    const overlay = document.querySelector('.user-dashboard-analytics-overlay');
    const toggleBtn = document.querySelector('.user-dashboard-analytics-toggle-btn');
    
    if (!sidebar || !overlay) return;
    
    analyticsState.userSidebarOpen = !analyticsState.userSidebarOpen;
    
    if (analyticsState.userSidebarOpen) {
        sidebar.classList.add('active');
        overlay.classList.add('active');
        toggleBtn.style.display = 'none';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling on mobile
    } else {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
        toggleBtn.style.display = 'flex';
        document.body.style.overflow = '';
    }
}

/**
 * Handle user analytics filter form submission
 */
function handleUserAnalyticsFilter(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    // Build query parameters
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            params.append(key, value);
        }
    }
    
    // Reload page with filters
    const currentUrl = new URL(window.location);
    const newUrl = `${currentUrl.pathname}?${params.toString()}`;
    
    // Show loading state
    showAnalyticsLoading();
    
    // Navigate to filtered results
    window.location.href = newUrl;
}

/**
 * Show distribution detail modal (user)
 */
function showUserDistributionDetail(distributionId) {
    // Reuse the same modal functionality as admin but with user-specific styling
    showDistributionDetail(distributionId);
}

// ===== UTILITY FUNCTIONS =====

/**
 * Show loading state in analytics sidebar
 */
function showAnalyticsLoading() {
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'analytics-loading-overlay';
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        z-index: 1200;
        display: flex;
        align-items: center;
        justify-content: center;
    `;
    
    loadingOverlay.innerHTML = `
        <div style="
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 1.5rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            color: #374151;
            font-weight: 500;
        ">
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="animation: spin 1s linear infinite;">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Memuat data analisis...
        </div>
    `;
    
    document.body.appendChild(loadingOverlay);
}

/**
 * Add CSS animation for spinning loader
 */
function addAnalyticsStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .analytics-detail-modal-overlay {
            animation: fadeIn 0.2s ease-out;
        }
        
        .analytics-detail-modal-content {
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(20px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
}

// ===== INITIALIZATION =====

/**
 * Initialize analytics sidebar functionality
 */
function initializeAnalyticsSidebar() {
    // Add necessary styles
    addAnalyticsStyles();
    
    // Set up form event listeners
    const adminForm = document.getElementById('analyticsFiltersForm');
    if (adminForm) {
        adminForm.addEventListener('submit', handleAdminAnalyticsFilter);
    }
    
    const userForm = document.getElementById('userAnalyticsFiltersForm');
    if (userForm) {
        userForm.addEventListener('submit', handleUserAnalyticsFilter);
    }
    
    // Handle escape key to close modals and sidebars
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (analyticsState.currentDetailModal) {
                closeDistributionDetail();
            } else if (analyticsState.adminSidebarOpen) {
                toggleAnalyticsSidebar();
            } else if (analyticsState.userSidebarOpen) {
                toggleUserAnalyticsSidebar();
            }
        }
    });
    
    // Handle window resize for mobile responsiveness
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            // Reset mobile-specific styles on desktop
            document.body.style.overflow = '';
        }
    });
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAnalyticsSidebar);
} else {
    initializeAnalyticsSidebar();
}
