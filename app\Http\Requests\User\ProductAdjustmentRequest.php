<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\StoreStock;

class ProductAdjustmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();
        
        // Check if user has a store
        if (!$user->store) {
            return false;
        }

        // Check if the product exists in the user's store
        $productId = $this->route('product');
        $storeStock = StoreStock::where('store_id', $user->store->id)
            ->where('product_id', $productId)
            ->first();

        return $storeStock !== null;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'adjustment_type' => 'required|in:subtract,set_zero',
            'quantity' => [
                'required_if:adjustment_type,subtract',
                'integer',
                'min:1',
                'max:999999', // Reasonable upper limit
            ],
            'notes' => 'nullable|string|max:500'
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'adjustment_type.required' => 'Silakan pilih jenis penyesuaian stok (Kurangi atau Habis).',
            'adjustment_type.in' => 'Jenis penyesuaian yang dipilih tidak valid. Pilih salah satu: Kurangi Stok atau Habis.',
            'quantity.required_if' => 'Jumlah pengurangan wajib diisi ketika memilih "Kurangi Stok". Masukkan jumlah yang akan dikurangi.',
            'quantity.integer' => 'Jumlah stok harus berupa angka bulat (tidak boleh desimal atau huruf).',
            'quantity.min' => 'Jumlah pengurangan harus lebih dari 0. Masukkan angka positif.',
            'quantity.max' => 'Jumlah pengurangan terlalu besar. Maksimal 999,999 unit per transaksi.',
            'notes.max' => 'Catatan terlalu panjang. Maksimal 500 karakter diperbolehkan.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateStockAvailability($validator);
        });
    }

    /**
     * Validate stock availability for the adjustment.
     */
    protected function validateStockAvailability($validator)
    {
        $adjustmentType = $this->input('adjustment_type');
        $quantity = $this->input('quantity', 0);

        if ($adjustmentType === 'subtract' && $quantity > 0) {
            $user = auth()->user();
            $productId = $this->route('product');

            $storeStock = StoreStock::where('store_id', $user->store->id)
                ->where('product_id', $productId)
                ->first();

            if ($storeStock) {
                if ($quantity > $storeStock->quantity) {
                    $validator->errors()->add(
                        'quantity',
                        "❌ Pengurangan stok gagal! Anda mencoba mengurangi {$quantity} unit, tetapi hanya tersedia {$storeStock->quantity} unit di toko.\n\n💡 Solusi:\n• Kurangi jumlah pengurangan (maksimal {$storeStock->quantity} unit)\n• Atau pilih 'Habis (Set ke 0)' untuk mengosongkan stok sepenuhnya"
                    );
                }
            } else {
                $validator->errors()->add(
                    'quantity',
                    '❌ Produk tidak ditemukan di stok toko Anda. Silakan refresh halaman dan coba lagi.'
                );
            }
        }

        // Additional validation for set_zero when stock is already zero
        if ($adjustmentType === 'set_zero') {
            $user = auth()->user();
            $productId = $this->route('product');

            $storeStock = StoreStock::where('store_id', $user->store->id)
                ->where('product_id', $productId)
                ->first();

            if ($storeStock && $storeStock->quantity == 0) {
                $validator->errors()->add(
                    'adjustment_type',
                    '💡 Stok produk ini sudah habis (0 unit). Tidak perlu melakukan penyesuaian.'
                );
            }
        }
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'adjustment_type' => 'jenis penyesuaian',
            'quantity' => 'jumlah',
            'notes' => 'catatan'
        ];
    }

    /**
     * Handle a failed authorization attempt.
     */
    protected function failedAuthorization()
    {
        abort(403, '🚫 Akses Ditolak: Anda tidak memiliki izin untuk mengelola produk ini atau produk tidak ditemukan di stok toko Anda. Silakan hubungi administrator jika ini adalah kesalahan.');
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Clean and prepare quantity input
        if ($this->has('quantity')) {
            $quantity = $this->input('quantity');

            // Remove any non-numeric characters except decimal point
            $cleanQuantity = preg_replace('/[^\d.]/', '', $quantity);

            // Convert to integer (remove decimal part)
            $intQuantity = (int) $cleanQuantity;

            $this->merge([
                'quantity' => $intQuantity > 0 ? $intQuantity : null
            ]);
        }

        // Trim notes
        if ($this->has('notes')) {
            $this->merge([
                'notes' => trim($this->input('notes'))
            ]);
        }
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function getValidationErrorsWithContext(): array
    {
        $errors = [];
        $user = auth()->user();
        $productId = $this->route('product');

        if ($user && $user->store && $productId) {
            $storeStock = StoreStock::where('store_id', $user->store->id)
                ->where('product_id', $productId)
                ->first();

            if ($storeStock) {
                $errors['context'] = [
                    'current_stock' => $storeStock->quantity,
                    'store_name' => $user->store->name,
                    'product_name' => $storeStock->product->name ?? 'Produk'
                ];
            }
        }

        return $errors;
    }
}
