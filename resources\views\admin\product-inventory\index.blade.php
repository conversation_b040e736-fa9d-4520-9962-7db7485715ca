@extends('layouts.admin')

@section('title', 'Inventori Produk - Indah Berkah Abadi')
@section('page-title', 'Inventori Produk')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Inventori Produk</h1>
                    <p class="text-gray-600 mt-1">Kelola stok produk di semua toko</p>
                </div>
                @if($selectedProduct)
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('admin.distributions.create', ['product_id' => $selectedProduct->id]) }}" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                        Buat Distribusi
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Product Selection -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" action="{{ route('admin.product-inventory.index') }}">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Pilih Produk</label>
                        <select name="product_id" id="product_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="this.form.submit()">
                            <option value="">-- Pilih Produk --</option>
                            @foreach($products as $product)
                                <option value="{{ $product->id }}" {{ request('product_id') == $product->id ? 'selected' : '' }}>
                                    {{ $product->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    @if($selectedProduct)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Cari Toko</label>
                            <div class="flex gap-2">
                                <input type="search" name="search" value="{{ request('search') }}" 
                                       placeholder="Nama toko atau lokasi..." class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    Cari
                                </button>
                            </div>
                        </div>
                    @endif
                </div>
            </form>
        </div>
    </div>

    @if($selectedProduct)
        <!-- Product Statistics - Compact Version -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-content">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ $productStats['total_stores'] ?? 0 }}</div>
                            <div class="text-xs text-gray-600">Total Toko</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ number_format($productStats['total_store_stock'] ?? 0) }}</div>
                            <div class="text-xs text-gray-600">Total Stok Toko</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ $productStats['pending_distributions'] ?? 0 }}</div>
                            <div class="text-xs text-gray-600">Distribusi Pending</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900">{{ $productStats['total_distributions'] ?? 0 }}</div>
                            <div class="text-xs text-gray-600">Total Distribusi</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Info -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Informasi Produk</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500">Nama Produk</div>
                            <div class="font-medium text-gray-900">{{ $selectedProduct->name }}</div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500">Stok Gudang</div>
                            <div class="font-medium text-gray-900">{{ number_format($selectedProduct->getCurrentWarehouseStock()) }} unit</div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500">Stok Tersedia</div>
                            <div class="font-medium text-gray-900">{{ number_format($selectedProduct->getAvailableStock()) }} unit</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stores Table -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Daftar Toko</h2>
            </div>
            <div class="admin-dashboard-card-content">
                @if($productStores->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3">Toko</th>
                                    <th class="px-6 py-3">Lokasi</th>
                                    <th class="px-6 py-3">Pengelola</th>
                                    <th class="px-6 py-3">Stok Saat Ini</th>
                                    <th class="px-6 py-3">Status</th>
                                    <th class="px-6 py-3">Total Distribusi</th>
                                    <th class="px-6 py-3">Total Diterima</th>
                                    <th class="px-6 py-3">Distribusi Terakhir</th>
                                    <th class="px-6 py-3">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($productStores as $store)
                                    <tr class="bg-white border-b hover:bg-gray-50">
                                        <td class="px-6 py-4">
                                            <div class="font-medium text-gray-900">{{ $store['store_name'] }}</div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-600">{{ $store['store_location'] }}</div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-600">{{ $store['user_name'] }}</div>
                                            <div class="text-xs text-gray-500">{{ $store['user_email'] }}</div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="font-medium text-gray-900">{{ number_format($store['current_stock']) }}</span>
                                            <span class="text-sm text-gray-500">unit</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full {{ $store['stock_status']['class'] }}">
                                                {{ $store['stock_status']['label'] }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="font-medium text-gray-900">{{ number_format($store['total_distributed']) }}</span>
                                            <span class="text-sm text-gray-500">unit</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="font-medium text-gray-900">{{ number_format($store['total_received']) }}</span>
                                            <span class="text-sm text-gray-500">unit</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            @if($store['last_distribution'])
                                                <div class="text-sm">
                                                    <div class="font-medium text-gray-900">{{ $store['last_distribution']->date_distributed->format('d/m/Y') }}</div>
                                                    <div class="text-gray-500">{{ number_format($store['last_distribution']->quantity) }} unit</div>
                                                </div>
                                            @else
                                                <span class="text-gray-400">Belum ada</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-2">
                                                <!-- <button type="button" 
                                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                                        onclick="openStockAdjustModal('{{ $store['store_id'] }}', '{{ $selectedProduct->id }}', '{{ $selectedProduct->name }}', {{ $store['current_stock'] }}, '{{ $store['store_name'] }}')">
                                                    Sesuaikan
                                                </button> -->
                                                <button type="button" 
                                                        class="text-green-600 hover:text-green-800 text-sm font-medium"
                                                        onclick="viewDistributionHistory('{{ $store['store_id'] }}', '{{ $selectedProduct->id }}', '{{ $selectedProduct->name }}', '{{ $store['store_name'] }}')">
                                                    Riwayat
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="py-8 text-center">
                        <div class="text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <p class="text-lg font-medium">Belum Ada Toko</p>
                            <p class="text-sm">Produk ini belum didistribusikan ke toko manapun.</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @else
        <!-- No Product Selected -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-content">
                <div class="py-8 text-center">
                    <div class="text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <p class="text-lg font-medium">Pilih Produk</p>
                        <p class="text-sm">Silakan pilih produk dari dropdown di atas untuk melihat inventori di semua toko.</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Stock Adjustment Modal -->
<div id="stockAdjustModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Sesuaikan Stok Produk</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeStockAdjustModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="admin-dashboard-modal-body">
            <form id="stockAdjustForm">
                @csrf
                <input type="hidden" id="adjust_store_id" name="store_id">
                <input type="hidden" id="adjust_product_id" name="product_id">
                
                <div class="admin-dashboard-form-group">
                    <label class="admin-dashboard-label">Produk</label>
                    <div id="adjust_product_name" class="admin-dashboard-info-display"></div>
                </div>

                <div class="admin-dashboard-form-group">
                    <label class="admin-dashboard-label">Toko</label>
                    <div id="adjust_store_name" class="admin-dashboard-info-display"></div>
                </div>

                <div class="admin-dashboard-form-group">
                    <label class="admin-dashboard-label">Stok Saat Ini</label>
                    <div id="adjust_current_stock" class="admin-dashboard-info-display"></div>
                </div>

                <div class="admin-dashboard-form-group">
                    <label for="adjustment_type" class="admin-dashboard-label">Jenis Penyesuaian</label>
                    <select name="adjustment_type" id="adjustment_type" class="admin-dashboard-select" required>
                        <option value="">-- Pilih Jenis --</option>
                        <option value="add">Tambah Stok</option>
                        <option value="subtract">Kurangi Stok</option>
                        <option value="set">Set Stok</option>
                    </select>
                </div>

                <div class="admin-dashboard-form-group">
                    <label for="quantity" class="admin-dashboard-label">Jumlah</label>
                    <input type="number" name="quantity" id="quantity" class="admin-dashboard-input" min="0" required>
                </div>

                <div class="admin-dashboard-form-group">
                    <label for="notes" class="admin-dashboard-label">Catatan (Opsional)</label>
                    <textarea name="notes" id="notes" class="admin-dashboard-textarea" rows="3" placeholder="Alasan penyesuaian stok..."></textarea>
                </div>
            </form>
        </div>
        <div class="admin-dashboard-modal-footer">
            <button type="button" class="admin-dashboard-btn admin-dashboard-btn-secondary" onclick="closeStockAdjustModal()">
                Batal
            </button>
            <button type="button" class="admin-dashboard-btn admin-dashboard-btn-primary" onclick="submitStockAdjustment()">
                Sesuaikan Stok
            </button>
        </div>
    </div>
</div>

<!-- Distribution History Modal -->
<div id="distributionHistoryModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content admin-dashboard-modal-large">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Riwayat Distribusi</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeDistributionHistoryModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="admin-dashboard-modal-body">
            <div id="distributionHistoryContent">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="{{ asset('js/admin-product-inventory.js') }}?v={{ time() }}"></script>
@endpush
