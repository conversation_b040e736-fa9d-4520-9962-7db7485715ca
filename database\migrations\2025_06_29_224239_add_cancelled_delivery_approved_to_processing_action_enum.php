<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, clean up any invalid values in processing_action column
        DB::statement("UPDATE returns SET processing_action = NULL WHERE processing_action NOT IN ('deleted', 'resent', 'accepted', 'rejected_final')");

        // For MySQL, we need to modify the enum column
        DB::statement("ALTER TABLE returns MODIFY COLUMN processing_action ENUM('deleted', 'resent', 'accepted', 'rejected_final', 'cancelled_delivery_approved') NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, clean up any values that won't fit in the old enum
        DB::statement("UPDATE returns SET processing_action = NULL WHERE processing_action = 'cancelled_delivery_approved'");

        // Remove the new enum value
        DB::statement("ALTER TABLE returns MODIFY COLUMN processing_action ENUM('deleted', 'resent', 'accepted', 'rejected_final') NULL");
    }
};
