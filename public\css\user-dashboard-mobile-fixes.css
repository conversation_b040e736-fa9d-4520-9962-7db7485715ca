/* PT. Indah Berkah Abadi - User Dashboard Mobile Z-Index Fixes */
/* Independent CSS for mobile accessibility and z-index management */
/*
 * COMPREHENSIVE Z-INDEX HIERARCHY FOR MOBILE ACCESSIBILITY:
 * - Sidebar navigation: 1000+ (highest priority)
 * - Modal overlays: 500-999 (high priority, below sidebar)
 * - Analytics components: 100-499 (medium-high priority)
 * - Interactive form elements: 50-99 (medium priority)
 * - Buttons and clickable elements: 10-49 (low-medium priority)
 * - Hover states and tooltips: 2-9 (low priority)
 * - Main content: 1 (lowest priority)
 */

/* ===== MOBILE-SPECIFIC Z-INDEX FIXES ===== */

/* Apply fixes only on mobile devices */
@media (max-width: 768px) {
    /* Sidebar - highest priority */
    .sidebar {
        z-index: 1000 !important;
        position: fixed !important;
    }

    /* Modal overlays */
    .user-dashboard-modal-overlay,
    .analytics-detail-modal-overlay {
        z-index: 500 !important;
        position: fixed !important;
    }

    /* Analytics components */
    .user-dashboard-analytics-toggle-btn {
        z-index: 150 !important;
        position: fixed !important;
    }

    .user-dashboard-analytics-sidebar {
        z-index: 120 !important;
        position: fixed !important;
    }

    .user-dashboard-analytics-overlay {
        z-index: 110 !important;
        position: fixed !important;
    }

    /* Enhanced form elements - highest interactive priority */
    .user-dashboard-form-select-enhanced,
    .user-dashboard-form-group-enhanced .user-dashboard-form-select,
    .user-dashboard-mobile-nav-enhanced {
        z-index: 60 !important;
        position: relative !important;
    }

    /* Enhanced form elements when focused */
    .user-dashboard-form-select-enhanced:focus,
    .user-dashboard-form-group-enhanced .user-dashboard-form-select:focus,
    .user-dashboard-mobile-nav-enhanced:focus {
        z-index: 70 !important;
        position: relative !important;
    }

    /* Enhanced containers */
    .user-dashboard-dropdown-container-enhanced,
    .user-dashboard-filter-group-enhanced,
    .user-dashboard-form-group-enhanced {
        z-index: 60 !important;
        position: relative !important;
    }

    /* Time period filter buttons */
    .user-dashboard-time-period-btn {
        z-index: 35 !important;
        position: relative !important;
    }

    /* Header action buttons */
    .user-dashboard-header-actions .user-dashboard-btn,
    .user-dashboard-header-actions .user-dashboard-btn-sm,
    .user-dashboard-header-actions a {
        z-index: 30 !important;
        position: relative !important;
    }

    /* List item action buttons */
    .user-dashboard-list-item-actions .user-dashboard-btn,
    .user-dashboard-list-item-actions .user-dashboard-btn-sm,
    .user-dashboard-list-item-actions a {
        z-index: 25 !important;
        position: relative !important;
    }

    /* Card action buttons */
    .user-dashboard-card-actions .user-dashboard-btn,
    .user-dashboard-card-actions .user-dashboard-btn-sm,
    .user-dashboard-card-actions a {
        z-index: 25 !important;
        position: relative !important;
    }

    /* General buttons */
    .user-dashboard-btn,
    .user-dashboard-btn-primary,
    .user-dashboard-btn-secondary,
    .user-dashboard-btn-sm {
        z-index: 20 !important;
        position: relative !important;
    }

    /* Form inputs and textareas */
    .user-dashboard-form-input,
    .user-dashboard-form-select,
    .user-dashboard-form-textarea {
        z-index: 15 !important;
        position: relative !important;
    }

    /* Dropdown menus */
    .user-dashboard-dropdown {
        z-index: 100 !important;
        position: absolute !important;
    }

    /* Pagination elements */
    .user-dashboard-pagination-btn {
        z-index: 20 !important;
        position: relative !important;
    }

    /* Search form elements */
    .user-dashboard-search-form .user-dashboard-form-input,
    .user-dashboard-search-form .user-dashboard-form-select {
        z-index: 25 !important;
        position: relative !important;
    }

    /* Profile form elements */
    .user-dashboard-profile-form .user-dashboard-form-input,
    .user-dashboard-profile-form .user-dashboard-form-select {
        z-index: 25 !important;
        position: relative !important;
    }

    /* Confirmation form elements */
    .user-dashboard-confirmation-form .user-dashboard-form-input,
    .user-dashboard-confirmation-form .user-dashboard-form-select {
        z-index: 25 !important;
        position: relative !important;
    }
}

/* ===== EXTRA SMALL MOBILE DEVICES ===== */
@media (max-width: 480px) {
    /* Increase z-index for very small screens */
    .user-dashboard-form-select-enhanced,
    .user-dashboard-form-group-enhanced .user-dashboard-form-select {
        z-index: 65 !important;
    }

    .user-dashboard-form-select-enhanced:focus,
    .user-dashboard-form-group-enhanced .user-dashboard-form-select:focus {
        z-index: 75 !important;
    }

    /* Ensure buttons remain clickable */
    .user-dashboard-btn,
    .user-dashboard-btn-primary,
    .user-dashboard-btn-secondary,
    .user-dashboard-btn-sm {
        z-index: 25 !important;
        min-height: 44px !important;
        min-width: 44px !important;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* High contrast mode support */
@media (prefers-contrast: high) and (max-width: 768px) {
    .user-dashboard-form-select-enhanced,
    .user-dashboard-form-input,
    .user-dashboard-btn {
        border-width: 3px !important;
        outline-width: 3px !important;
    }
}

/* Focus indicators for mobile */
@media (max-width: 768px) {
    .user-dashboard-form-select-enhanced:focus,
    .user-dashboard-form-input:focus,
    .user-dashboard-btn:focus {
        outline: 2px solid #3b82f6 !important;
        outline-offset: 2px !important;
        z-index: 80 !important;
    }
}

/* ===== TOUCH TARGET IMPROVEMENTS ===== */
@media (max-width: 768px) {
    /* Ensure all interactive elements meet 44px minimum touch target */
    .user-dashboard-btn,
    .user-dashboard-btn-sm,
    .user-dashboard-form-select,
    .user-dashboard-form-input,
    .user-dashboard-time-period-btn {
        min-height: 44px !important;
        min-width: 44px !important;
        touch-action: manipulation !important;
    }

    /* Improve spacing for touch targets */
    .user-dashboard-list-item-actions {
        gap: 0.75rem !important;
    }

    .user-dashboard-header-actions {
        gap: 0.75rem !important;
    }
}

/* ===== DEBUGGING HELPERS ===== */
/* Uncomment to visualize z-index layers during development */
/*
@media (max-width: 768px) {
    .user-dashboard-form-select-enhanced {
        box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.5) !important;
    }
    
    .user-dashboard-btn {
        box-shadow: 0 0 0 2px rgba(0, 255, 0, 0.5) !important;
    }
    
    .user-dashboard-time-period-btn {
        box-shadow: 0 0 0 2px rgba(0, 0, 255, 0.5) !important;
    }
}
*/
