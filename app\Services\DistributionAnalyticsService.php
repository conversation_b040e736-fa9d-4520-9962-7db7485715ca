<?php

namespace App\Services;

use App\Models\Distribution;
use App\Models\Store;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DistributionAnalyticsService
{
    /**
     * Get simple distribution comparison data for admin dashboard (all stores)
     */
    public function getAdminAnalytics($filters = [])
    {
        // Get recent confirmed distributions (last 30 days)
        $query = Distribution::with(['product', 'store'])
            ->where('confirmed', true)
            ->where('date_distributed', '>=', Carbon::now()->subDays(30));

        // Apply warehouse filter if provided
        if (isset($filters['warehouse_id']) && $filters['warehouse_id']) {
            $query->where('warehouse_id', $filters['warehouse_id']);
        }

        $distributions = $query->orderBy('date_distributed', 'desc')
            ->limit(20)
            ->get();

        return [
            'summary' => $this->calculateBasicSummary($distributions),
            'recent_distributions' => $this->formatDistributionsList($distributions)
        ];
    }

    /**
     * Get simple distribution comparison data for user dashboard (specific store only)
     */
    public function getUserAnalytics($storeId, $filters = [])
    {
        // Get recent confirmed distributions for this store (last 30 days)
        $distributions = Distribution::with(['product', 'store'])
            ->where('store_id', $storeId)
            ->where('confirmed', true)
            ->where('date_distributed', '>=', Carbon::now()->subDays(30))
            ->orderBy('date_distributed', 'desc')
            ->limit(15)
            ->get();

        return [
            'summary' => $this->calculateBasicSummary($distributions),
            'recent_distributions' => $this->formatDistributionsList($distributions)
        ];
    }

    /**
     * Calculate basic summary statistics
     */
    private function calculateBasicSummary($distributions)
    {
        $totalSent = $distributions->sum('quantity');
        $totalReceived = $distributions->sum('received_quantity');
        $totalDistributions = $distributions->count();
        $totalDifference = $totalReceived - $totalSent;

        return [
            'total_sent' => $totalSent,
            'total_received' => $totalReceived,
            'total_distributions' => $totalDistributions,
            'total_difference' => $totalDifference,
            'difference_type' => $totalDifference < 0 ? 'shortage' : ($totalDifference > 0 ? 'overage' : 'perfect')
        ];
    }

    /**
     * Format distributions list for display
     */
    private function formatDistributionsList($distributions)
    {
        return $distributions->map(function ($dist) {
            $difference = $dist->received_quantity - $dist->quantity;

            return [
                'id' => $dist->id,
                'product_name' => $dist->product->name ?? 'Produk Tidak Diketahui',
                'store_name' => $dist->store->name ?? 'Toko Tidak Diketahui',
                'quantity_sent' => $dist->quantity,
                'quantity_received' => $dist->received_quantity,
                'difference' => $difference,
                'difference_type' => $difference < 0 ? 'shortage' : ($difference > 0 ? 'overage' : 'perfect'),
                'date_distributed' => $dist->date_distributed,
                'notes' => $dist->notes,
                'formatted_date' => $dist->date_distributed ? $dist->date_distributed->format('d/m/Y') : '-'
            ];
        })->values();
    }

}
