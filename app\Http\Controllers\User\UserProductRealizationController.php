<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\TimePeriodFilter;
use App\Models\Distribution;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserProductRealizationController extends Controller
{
    use TimePeriodFilter;
    /**
     * Display product realization data for the logged-in user's store
     */
    public function index(Request $request)
    {
        // Get the current user's store
        $user = Auth::user();
        $userStore = $user->store;

        if (!$userStore) {
            return redirect()->route('user.dashboard')
                ->with('error', 'Anda tidak terdaftar di toko manapun. Silakan hubungi administrator.');
        }

        // Get filter parameters
        $productFilter = $request->get('product');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        // Build query for confirmed distributions with realization data for this store only
        $query = Distribution::with(['product', 'store'])
            ->where('store_id', $userStore->id)
            ->where('confirmed', true)
            ->whereNotNull('received_quantity');

        // Apply time period filter
        $this->applyTimePeriodFilterDate($query, $request, 'date_distributed');

        // Get date range for form display
        $dateRange = $this->getDateRangeFromPeriod($request);
        $dateFrom = $dateRange['start'] ? $dateRange['start']->format('Y-m-d') : null;
        $dateTo = $dateRange['end'] ? $dateRange['end']->format('Y-m-d') : null;

        // Manual date override (if provided, override time period)
        if ($request->filled('date_from') || $request->filled('date_to')) {
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            if ($dateFrom) {
                $query->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->whereDate('date_distributed', '<=', $dateTo);
            }
        }

        // Apply other filters
        if ($productFilter) {
            $query->where('product_id', $productFilter);
        }

        // Order by most recent first
        $query->orderBy('date_distributed', 'desc');

        // Paginate results
        $distributions = $query->paginate(10);

        // Calculate realization statistics for this store
        $stats = $this->calculateStoreRealizationStats($query, $userStore);

        // Get products that have been distributed to this store for filter options
        $products = Product::whereHas('distributions', function ($q) use ($userStore) {
            $q->where('store_id', $userStore->id)
              ->where('confirmed', true)
              ->whereNotNull('received_quantity');
        })->orderBy('name')->get();

        return view('user.product-realization.index', compact(
            'distributions',
            'stats',
            'products',
            'userStore',
            'productFilter',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Calculate realization statistics for a specific store
     */
    private function calculateStoreRealizationStats($query, $store)
    {
        // Clone query for statistics calculation
        $statsQuery = clone $query;
        $allDistributions = $statsQuery->get();

        $totalDistributions = $allDistributions->count();
        $totalPlanned = $allDistributions->sum('quantity');
        $totalReceived = $allDistributions->sum('received_quantity');
        $totalDifference = $totalReceived - $totalPlanned;

        // Calculate variance categories
        $perfectMatch = $allDistributions->filter(function ($dist) {
            return $dist->received_quantity == $dist->quantity;
        })->count();
        
        $shortage = $allDistributions->filter(function ($dist) {
            return $dist->received_quantity < $dist->quantity;
        })->count();
        
        $overage = $allDistributions->filter(function ($dist) {
            return $dist->received_quantity > $dist->quantity;
        })->count();

        return [
            'store_name' => $store->name,
            'total_distributions' => $totalDistributions,
            'total_planned' => $totalPlanned,
            'total_received' => $totalReceived,
            'total_difference' => $totalDifference,
            'perfect_match' => $perfectMatch,
            'shortage' => $shortage,
            'overage' => $overage,
            'realization_percentage' => $totalPlanned > 0 ? round(($totalReceived / $totalPlanned) * 100, 2) : 0,
        ];
    }
}
