<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class SupplierDelivery extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'supplier_id',
        'product_id',
        'quantity',
        'received_quantity',
        'unit_price',
        'total_price',
        'delivery_date',
        'received_date',
        'status',
        'notes',
        'received_by',
        'warehouse_id',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'received_quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'delivery_date' => 'date',
        'received_date' => 'date',
        'status' => 'string',
    ];

    /**
     * Get the supplier for the delivery.
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the product for the delivery.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who received the delivery.
     */
    public function receivedBy()
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Get the warehouse for the delivery.
     */
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * Get the returns for this delivery.
     */
    public function returns()
    {
        return $this->hasMany(ReturnModel::class);
    }

    /**
     * Scope a query to only include pending deliveries.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include received deliveries.
     */
    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }

    /**
     * Scope a query to only include partial deliveries.
     */
    public function scopePartial($query)
    {
        return $query->where('status', 'partial');
    }

    /**
     * Scope a query to only include cancelled deliveries.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Check if delivery is fully received.
     */
    public function isFullyReceived()
    {
        return $this->status === 'received' && $this->received_quantity >= $this->quantity;
    }

    /**
     * Check if delivery is partially received.
     */
    public function isPartiallyReceived()
    {
        return $this->status === 'partial' && $this->received_quantity > 0 && $this->received_quantity < $this->quantity;
    }

    /**
     * Get the remaining quantity to be received.
     */
    public function getRemainingQuantityAttribute()
    {
        return $this->quantity - ($this->received_quantity ?? 0);
    }

    /**
     * Get the delivery completion percentage.
     */
    public function getCompletionPercentageAttribute()
    {
        if ($this->quantity == 0) return 0;
        return round((($this->received_quantity ?? 0) / $this->quantity) * 100, 2);
    }
}
