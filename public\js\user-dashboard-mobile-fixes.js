/**
 * PT. Indah Berkah Abadi - User Dashboard Mobile Z-Index Fixes
 * Independent JavaScript for mobile accessibility and interaction management
 * 
 * Features:
 * - Dynamic z-index management for mobile devices
 * - Touch interaction optimization
 * - Conflict prevention with sidebar navigation
 * - Accessibility support for mobile users
 * - Real-time responsive adjustments
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        DEBUG: false,
        MOBILE_BREAKPOINT: 768,
        SMALL_MOBILE_BREAKPOINT: 480,
        Z_INDEX: {
            SIDEBAR: 1000,
            MODAL: 500,
            ANALYTICS_TOGGLE: 150,
            ANALYTICS_SIDEBAR: 120,
            ANALYTICS_OVERLAY: 110,
            DROPDOWN_FOCUS: 70,
            DROPDOWN_ENHANCED: 60,
            TIME_PERIOD_BTN: 35,
            HEADER_ACTIONS: 30,
            LIST_ACTIONS: 25,
            BUTTONS: 20,
            FORM_ELEMENTS: 15,
            FOCUS_STATE: 80
        }
    };

    // Debug logging
    function log(message, data = null) {
        if (CONFIG.DEBUG) {
            console.log('[UserDashboardMobileFixes]', message, data || '');
        }
    }

    // Initialize when D<PERSON> is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    function initialize() {
        log('Initializing user dashboard mobile fixes...');

        // Apply initial fixes
        applyMobileFixes();

        // Set up event listeners
        setupEventListeners();

        // Set up responsive monitoring
        setupResponsiveMonitoring();

        log('User dashboard mobile fixes initialized successfully');
    }

    /**
     * Apply mobile-specific fixes
     */
    function applyMobileFixes() {
        if (!isMobileDevice()) {
            log('Not a mobile device, skipping mobile fixes');
            return;
        }

        log('Applying mobile fixes...');

        // Fix form elements
        fixFormElements();

        // Fix buttons
        fixButtons();

        // Fix interactive elements
        fixInteractiveElements();

        // Fix touch targets
        fixTouchTargets();

        log('Mobile fixes applied successfully');
    }

    /**
     * Fix form elements z-index
     */
    function fixFormElements() {
        // Enhanced form selects
        const enhancedSelects = document.querySelectorAll('.user-dashboard-form-select-enhanced, .user-dashboard-form-group-enhanced .user-dashboard-form-select');
        enhancedSelects.forEach(element => {
            setElementZIndex(element, CONFIG.Z_INDEX.DROPDOWN_ENHANCED);
            
            // Add focus/blur handlers
            element.addEventListener('focus', () => {
                setElementZIndex(element, CONFIG.Z_INDEX.DROPDOWN_FOCUS);
            });
            
            element.addEventListener('blur', () => {
                setElementZIndex(element, CONFIG.Z_INDEX.DROPDOWN_ENHANCED);
            });
        });

        // Regular form elements
        const formElements = document.querySelectorAll('.user-dashboard-form-input, .user-dashboard-form-select, .user-dashboard-form-textarea');
        formElements.forEach(element => {
            setElementZIndex(element, CONFIG.Z_INDEX.FORM_ELEMENTS);
        });
    }

    /**
     * Fix buttons z-index
     */
    function fixButtons() {
        // Header action buttons
        const headerButtons = document.querySelectorAll('.user-dashboard-header-actions .user-dashboard-btn, .user-dashboard-header-actions .user-dashboard-btn-sm, .user-dashboard-header-actions a');
        headerButtons.forEach(button => {
            setElementZIndex(button, CONFIG.Z_INDEX.HEADER_ACTIONS);
        });

        // List item action buttons
        const listButtons = document.querySelectorAll('.user-dashboard-list-item-actions .user-dashboard-btn, .user-dashboard-list-item-actions .user-dashboard-btn-sm, .user-dashboard-list-item-actions a');
        listButtons.forEach(button => {
            setElementZIndex(button, CONFIG.Z_INDEX.LIST_ACTIONS);
        });

        // General buttons
        const buttons = document.querySelectorAll('.user-dashboard-btn, .user-dashboard-btn-primary, .user-dashboard-btn-secondary, .user-dashboard-btn-sm');
        buttons.forEach(button => {
            setElementZIndex(button, CONFIG.Z_INDEX.BUTTONS);
        });

        // Time period filter buttons
        const timePeriodButtons = document.querySelectorAll('.user-dashboard-time-period-btn');
        timePeriodButtons.forEach(button => {
            setElementZIndex(button, CONFIG.Z_INDEX.TIME_PERIOD_BTN);
        });
    }

    /**
     * Fix interactive elements
     */
    function fixInteractiveElements() {
        // Analytics toggle button
        const analyticsToggle = document.querySelector('.user-dashboard-analytics-toggle-btn');
        if (analyticsToggle) {
            analyticsToggle.style.position = 'fixed';
            analyticsToggle.style.zIndex = CONFIG.Z_INDEX.ANALYTICS_TOGGLE;
        }

        // Enhanced containers
        const containers = document.querySelectorAll('.user-dashboard-dropdown-container-enhanced, .user-dashboard-filter-group-enhanced, .user-dashboard-form-group-enhanced');
        containers.forEach(container => {
            setElementZIndex(container, CONFIG.Z_INDEX.DROPDOWN_ENHANCED);
        });
    }

    /**
     * Fix touch targets
     */
    function fixTouchTargets() {
        const interactiveElements = document.querySelectorAll('.user-dashboard-btn, .user-dashboard-btn-sm, .user-dashboard-form-select, .user-dashboard-form-input, .user-dashboard-time-period-btn');
        
        interactiveElements.forEach(element => {
            // Ensure minimum touch target size
            const computedStyle = window.getComputedStyle(element);
            const minHeight = parseInt(computedStyle.minHeight) || 0;
            const minWidth = parseInt(computedStyle.minWidth) || 0;
            
            if (minHeight < 44) {
                element.style.minHeight = '44px';
            }
            
            if (minWidth < 44) {
                element.style.minWidth = '44px';
            }
            
            // Add touch-action
            element.style.touchAction = 'manipulation';
        });
    }

    /**
     * Set element z-index with proper positioning
     */
    function setElementZIndex(element, zIndex) {
        if (!element) return;
        
        element.style.position = element.style.position || 'relative';
        element.style.zIndex = zIndex;
    }

    /**
     * Check if current device is mobile
     */
    function isMobileDevice() {
        return window.innerWidth <= CONFIG.MOBILE_BREAKPOINT;
    }

    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        // Focus management for accessibility
        document.addEventListener('focusin', handleFocusIn);
        document.addEventListener('focusout', handleFocusOut);

        // Touch event optimization
        document.addEventListener('touchstart', handleTouchStart, { passive: true });
    }

    /**
     * Handle focus in events
     */
    function handleFocusIn(e) {
        if (!isMobileDevice()) return;

        const element = e.target;
        
        // Boost z-index for focused elements
        if (element.matches('.user-dashboard-form-select-enhanced, .user-dashboard-form-input, .user-dashboard-btn')) {
            element.style.zIndex = CONFIG.Z_INDEX.FOCUS_STATE;
        }
    }

    /**
     * Handle focus out events
     */
    function handleFocusOut(e) {
        if (!isMobileDevice()) return;

        const element = e.target;
        
        // Restore original z-index
        if (element.matches('.user-dashboard-form-select-enhanced')) {
            element.style.zIndex = CONFIG.Z_INDEX.DROPDOWN_ENHANCED;
        } else if (element.matches('.user-dashboard-form-input')) {
            element.style.zIndex = CONFIG.Z_INDEX.FORM_ELEMENTS;
        } else if (element.matches('.user-dashboard-btn')) {
            element.style.zIndex = CONFIG.Z_INDEX.BUTTONS;
        }
    }

    /**
     * Handle touch start events
     */
    function handleTouchStart(e) {
        // Ensure touch target is accessible
        const element = e.target;
        if (element.matches('.user-dashboard-btn, .user-dashboard-form-select, .user-dashboard-form-input')) {
            // Temporarily boost z-index during touch
            const originalZIndex = element.style.zIndex;
            element.style.zIndex = CONFIG.Z_INDEX.FOCUS_STATE;
            
            // Restore after touch
            setTimeout(() => {
                element.style.zIndex = originalZIndex;
            }, 100);
        }
    }

    /**
     * Set up responsive monitoring
     */
    function setupResponsiveMonitoring() {
        // Monitor window resize
        window.addEventListener('resize', debounce(handleResize, 250));
        
        // Monitor orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(handleOrientationChange, 100);
        });
    }

    /**
     * Handle window resize
     */
    function handleResize() {
        if (isMobileDevice()) {
            applyMobileFixes();
        }
    }

    /**
     * Handle orientation change
     */
    function handleOrientationChange() {
        applyMobileFixes();
    }

    /**
     * Debounce utility function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Public API
     */
    window.UserDashboardMobileFixes = {
        reinitialize: initialize,
        applyFixes: applyMobileFixes,
        enableDebug: function() {
            CONFIG.DEBUG = true;
            log('Debug mode enabled');
        },
        disableDebug: function() {
            CONFIG.DEBUG = false;
        },
        isMobile: isMobileDevice
    };

    log('User dashboard mobile fixes script loaded successfully');
})();
