<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\TimePeriodFilter;
use App\Models\Distribution;
use App\Models\Product;
use App\Models\Store;
use App\Services\ExcelExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminProductRealizationController extends Controller
{
    use TimePeriodFilter;
    /**
     * Display product realization data for all stores
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $storeFilter = $request->get('store');
        $productFilter = $request->get('product');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        // Default to 'all' period for product realization to show all data
        if (!$request->has('period')) {
            $request->merge(['period' => 'all']);
        }

        // Build query for confirmed distributions with realization data
        $query = Distribution::with(['product', 'store'])
            ->where('confirmed', true)
            ->whereNotNull('received_quantity');

        // Apply time period filter
        $this->applyTimePeriodFilterDate($query, $request, 'date_distributed');

        // Get date range for form display
        $dateRange = $this->getDateRangeFromPeriod($request);
        $dateFrom = $dateRange['start'] ? $dateRange['start']->format('Y-m-d') : null;
        $dateTo = $dateRange['end'] ? $dateRange['end']->format('Y-m-d') : null;

        // Manual date override (if provided, override time period)
        if ($request->filled('date_from') || $request->filled('date_to')) {
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            if ($dateFrom) {
                $query->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->whereDate('date_distributed', '<=', $dateTo);
            }
        }

        // Apply other filters
        if ($storeFilter) {
            $query->where('store_id', $storeFilter);
        }

        if ($productFilter) {
            $query->where('product_id', $productFilter);
        }

        // Order by most recent first
        $query->orderBy('date_distributed', 'desc');

        // Paginate results
        $distributions = $query->paginate(15);

        // Calculate realization statistics
        $stats = $this->calculateRealizationStats($query);

        // Get filter options
        $stores = Store::orderBy('name')->get();
        $products = Product::orderBy('name')->get();

        return view('admin.product-realization.index', compact(
            'distributions',
            'stats',
            'stores',
            'products',
            'storeFilter',
            'productFilter',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Calculate realization statistics
     */
    private function calculateRealizationStats($query)
    {
        // Clone query for statistics calculation
        $statsQuery = clone $query;
        $allDistributions = $statsQuery->get();

        $totalDistributions = $allDistributions->count();
        $totalPlanned = $allDistributions->sum('quantity');
        $totalReceived = $allDistributions->sum('received_quantity');
        $totalDifference = $totalReceived - $totalPlanned;

        // Calculate variance categories
        $perfectMatch = $allDistributions->filter(function ($dist) {
            return $dist->received_quantity == $dist->quantity;
        })->count();
        $shortage = $allDistributions->filter(function ($dist) {
            return $dist->received_quantity < $dist->quantity;
        })->count();
        $overage = $allDistributions->filter(function ($dist) {
            return $dist->received_quantity > $dist->quantity;
        })->count();

        return [
            'total_distributions' => $totalDistributions,
            'total_planned' => $totalPlanned,
            'total_received' => $totalReceived,
            'total_difference' => $totalDifference,
            'perfect_match' => $perfectMatch,
            'shortage' => $shortage,
            'overage' => $overage,
            'realization_percentage' => $totalPlanned > 0 ? round(($totalReceived / $totalPlanned) * 100, 2) : 0,
        ];
    }

    /**
     * Export product realization data to Excel
     */
    public function export(Request $request, ExcelExportService $excelService)
    {
        // Get user's timezone
        $userTimezone = auth()->user()->timezone ?? 'Asia/Jakarta';

        // Default to 'all' period for product realization to show all data
        if (!$request->has('period')) {
            $request->merge(['period' => 'all']);
        }

        // Apply same filters as index method
        $query = Distribution::with(['product', 'store'])
            ->where('confirmed', true)
            ->whereNotNull('received_quantity');

        // Apply time period filter
        $this->applyTimePeriodFilterDate($query, $request, 'date_distributed');

        // Get date range for form display
        $dateRange = $this->getDateRangeFromPeriod($request);
        $dateFrom = $dateRange['start'] ? $dateRange['start']->format('Y-m-d') : null;
        $dateTo = $dateRange['end'] ? $dateRange['end']->format('Y-m-d') : null;

        // Manual date override
        if ($request->filled('date_from') || $request->filled('date_to')) {
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            if ($dateFrom) {
                $query->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->whereDate('date_distributed', '<=', $dateTo);
            }
        }

        // Apply other filters
        $storeFilter = $request->get('store');
        $productFilter = $request->get('product');

        if ($storeFilter) {
            $query->where('store_id', $storeFilter);
        }

        if ($productFilter) {
            $query->where('product_id', $productFilter);
        }

        // Order by most recent first
        $query->orderBy('date_distributed', 'desc');

        $distributions = $query->get();

        // Prepare worksheets data
        $worksheets = [];

        // Product Realization worksheet
        $realizationData = [];
        $no = 1;
        foreach ($distributions as $distribution) {
            $dateDistributed = $excelService->formatDateOnly($distribution->date_distributed, $userTimezone);
            $productName = $distribution->product ? $distribution->product->name : 'N/A';
            $storeName = $distribution->store ? $distribution->store->name : 'N/A';
            $storeLocation = $distribution->store ? $distribution->store->location : 'N/A';
            $quantitySent = $distribution->quantity;
            $quantityReceived = $distribution->received_quantity ?? 0;
            $difference = $quantityReceived - $quantitySent;
            $realizationPercentage = $quantitySent > 0 ? round(($quantityReceived / $quantitySent) * 100, 2) : 0;
            $status = $distribution->confirmed ? 'Dikonfirmasi' : 'Belum Dikonfirmasi';
            $notes = $distribution->notes ?? '';

            $realizationData[] = [
                $no++,
                $dateDistributed,
                $productName,
                $storeName,
                $storeLocation,
                $quantitySent,
                $quantityReceived,
                $difference,
                $realizationPercentage . '%',
                $status,
                $notes
            ];
        }

        $subtitle = '';
        if ($dateFrom && $dateTo) {
            $subtitle = 'Periode: ' . \Carbon\Carbon::parse($dateFrom)->format('d/m/Y') . ' - ' . \Carbon\Carbon::parse($dateTo)->format('d/m/Y');
        }

        $worksheets[] = [
            'name' => 'Realisasi Produk',
            'title' => 'REALISASI PRODUK - INDAH BERKAH ABADI',
            'subtitle' => $subtitle,
            'headers' => ['No', 'Tanggal', 'Produk', 'Toko', 'Lokasi', 'Dikirim', 'Diterima', 'Selisih', 'Realisasi (%)', 'Status', 'Catatan'],
            'data' => $realizationData
        ];

        // Create filename
        $startDate = $dateFrom ? \Carbon\Carbon::parse($dateFrom) : null;
        $endDate = $dateTo ? \Carbon\Carbon::parse($dateTo) : null;
        $filename = $excelService->createFilename('realisasi_produk', $startDate, $endDate);

        return $excelService->generateExcelFile($worksheets, $filename, $userTimezone);
    }
}
