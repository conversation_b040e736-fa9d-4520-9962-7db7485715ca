<?php

namespace App\Http\Controllers\Traits;

use Carbon\Carbon;
use Illuminate\Http\Request;

trait TimePeriodFilter
{
    /**
     * Get date range based on time period parameter
     */
    protected function getDateRangeFromPeriod(Request $request): array
    {
        $period = $request->get('period', 'month');
        $now = Carbon::now();

        switch ($period) {
            case 'day':
                return [
                    'start' => $now->copy()->startOfDay(),
                    'end' => $now->copy()->endOfDay(),
                    'period' => 'day'
                ];

            case 'week':
                return [
                    'start' => $now->copy()->startOfWeek(),
                    'end' => $now->copy()->endOfWeek(),
                    'period' => 'week'
                ];

            case 'month':
                return [
                    'start' => $now->copy()->startOfMonth(),
                    'end' => $now->copy()->endOfMonth(),
                    'period' => 'month'
                ];

            case 'all':
                return [
                    'start' => null,
                    'end' => null,
                    'period' => 'all'
                ];

            default:
                // Default to current month for performance
                return [
                    'start' => $now->copy()->startOfMonth(),
                    'end' => $now->copy()->endOfMonth(),
                    'period' => 'month'
                ];
        }
    }

    /**
     * Apply time period filter to query
     */
    protected function applyTimePeriodFilter($query, Request $request, string $dateColumn = 'created_at')
    {
        $dateRange = $this->getDateRangeFromPeriod($request);

        if ($dateRange['start'] && $dateRange['end']) {
            $query->whereBetween($dateColumn, [
                $dateRange['start']->format('Y-m-d H:i:s'),
                $dateRange['end']->format('Y-m-d H:i:s')
            ]);
        }

        return $query;
    }

    /**
     * Apply time period filter to query using date only (for date_distributed, etc.)
     */
    protected function applyTimePeriodFilterDate($query, Request $request, string $dateColumn = 'date_distributed')
    {
        $dateRange = $this->getDateRangeFromPeriod($request);

        if ($dateRange['start'] && $dateRange['end']) {
            $query->whereDate($dateColumn, '>=', $dateRange['start']->format('Y-m-d'))
                  ->whereDate($dateColumn, '<=', $dateRange['end']->format('Y-m-d'));
        }

        return $query;
    }

    /**
     * Get period label for display
     */
    protected function getPeriodLabel(Request $request): string
    {
        $period = $request->get('period', 'month');
        $now = Carbon::now();

        switch ($period) {
            case 'day':
                return 'Hari Ini (' . $now->format('d F Y') . ')';
            case 'week':
                return 'Minggu Ini (' . $now->startOfWeek()->format('d M') . ' - ' . $now->endOfWeek()->format('d M Y') . ')';
            case 'month':
                return 'Bulan Ini (' . $now->format('F Y') . ')';
            case 'all':
                return 'Semua Waktu';
            default:
                return 'Bulan Ini (' . $now->format('F Y') . ')';
        }
    }

    /**
     * Get period-specific statistics for distributions
     */
    protected function getPeriodDistributionStats(Request $request, $baseQuery = null)
    {
        $dateRange = $this->getDateRangeFromPeriod($request);
        
        if (!$baseQuery) {
            $baseQuery = \App\Models\Distribution::query();
        }

        $query = clone $baseQuery;

        if ($dateRange['start'] && $dateRange['end']) {
            $query->whereDate('date_distributed', '>=', $dateRange['start']->format('Y-m-d'))
                  ->whereDate('date_distributed', '<=', $dateRange['end']->format('Y-m-d'));
        }

        return [
            'total' => $query->count(),
            'confirmed' => $query->where('confirmed', true)->count(),
            'unconfirmed' => $query->where('confirmed', false)->count(),
            'period_label' => $this->getPeriodLabel($request)
        ];
    }

    /**
     * Get period-specific statistics for products
     */
    protected function getPeriodProductStats(Request $request)
    {
        $dateRange = $this->getDateRangeFromPeriod($request);
        
        $stats = [
            'total_products' => \App\Models\Product::count(),
            'warehouse_stock' => \App\Models\WarehouseStock::sum('quantity'),
            'store_stock' => \App\Models\StoreStock::sum('quantity'),
            'period_label' => $this->getPeriodLabel($request)
        ];

        // Add period-specific distribution count
        $distributionQuery = \App\Models\Distribution::query();
        if ($dateRange['start'] && $dateRange['end']) {
            $distributionQuery->whereDate('date_distributed', '>=', $dateRange['start']->format('Y-m-d'))
                             ->whereDate('date_distributed', '<=', $dateRange['end']->format('Y-m-d'));
        }
        $stats['distributions'] = $distributionQuery->count();

        return $stats;
    }
}
