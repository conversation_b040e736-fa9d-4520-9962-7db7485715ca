<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Specific Form Fixes - PT. Indah Berkah <PERSON></title>
    
    <!-- Include the form CSS -->
    <link rel="stylesheet" href="css/user-dashboard-forms.css">
    <link rel="stylesheet" href="css/user-dashboard-dropdowns.css">
    
    <!-- Basic styling for test page -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
            line-height: 1.6;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            background: #fafafa;
        }
        
        .test-title {
            font-size: 1.75rem;
            font-weight: bold;
            color: #111827;
            margin-bottom: 1rem;
        }
        
        .test-subtitle {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
        }
        
        .test-description {
            color: #6b7280;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }
        
        .sidebar-mock {
            position: fixed;
            top: 0;
            left: 0;
            width: 16rem;
            height: 100vh;
            background-color: #166534;
            z-index: 1000;
            padding: 1rem;
            color: white;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }
        
        .sidebar-mock.show {
            transform: translateX(0);
        }
        
        .main-content {
            margin-left: 0;
            transition: margin-left 0.3s ease;
        }
        
        .main-content.sidebar-open {
            margin-left: 17rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .test-card {
            background: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }
        
        .status-indicator {
            padding: 0.5rem;
            border-radius: 0.375rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }
        
        .status-success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .test-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
            font-size: 0.875rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .test-button:hover {
            background-color: #2563eb;
        }
        
        .toggle-button {
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background-color: #166534;
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.875rem;
        }
        
        .issue-highlight {
            background-color: #fef3c7;
            border: 2px solid #f59e0b;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .fix-highlight {
            background-color: #d1fae5;
            border: 2px solid #10b981;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .main-content.sidebar-open {
                margin-left: 0;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Toggle Button -->
    <button class="toggle-button" onclick="toggleSidebar()">
        ☰ Toggle Sidebar
    </button>

    <!-- Mock Sidebar -->
    <div class="sidebar-mock" id="mockSidebar">
        <h3>PT. Indah Berkah Abadi</h3>
        <p>Mock Sidebar (z-index: 1000)</p>
        <p style="font-size: 0.75rem; margin-top: 1rem;">
            This sidebar should remain accessible while all form elements work properly below it.
        </p>
        <div style="margin-top: 2rem; font-size: 0.75rem;">
            <p><strong>Test Instructions:</strong></p>
            <ol style="margin-left: 1rem; margin-top: 0.5rem;">
                <li>Keep sidebar open</li>
                <li>Try clicking form inputs</li>
                <li>Try typing in inputs</li>
                <li>Verify all work properly</li>
            </ol>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="test-container">
            <h1 class="test-title">Specific Form Fixes Test</h1>
            <p class="test-description">
                This page tests the specific form interaction fixes for `/user/profile/edit` and `/user/settings` pages.
                The reported issues were that input fields could not be clicked or typed into due to z-index conflicts.
            </p>

            <!-- Issue Summary -->
            <div class="issue-highlight">
                <h3 style="margin: 0 0 0.5rem 0; color: #92400e;">🚨 Issues Reported:</h3>
                <ul style="margin: 0; padding-left: 1.5rem; color: #92400e;">
                    <li><strong>/user/profile/edit:</strong> "Nama Lengkap" and "Email" inputs blocked</li>
                    <li><strong>/user/settings:</strong> "Nama Lengkap", "Email", "Password Baru", "Konfirmasi Password" inputs blocked</li>
                    <li><strong>/user/settings:</strong> Mobile navigation dropdown selection issues</li>
                </ul>
            </div>

            <!-- Fix Summary -->
            <div class="fix-highlight">
                <h3 style="margin: 0 0 0.5rem 0; color: #065f46;">✅ Fixes Applied:</h3>
                <ul style="margin: 0; padding-left: 1.5rem; color: #065f46;">
                    <li>Added enhanced CSS classes with proper z-index hierarchy</li>
                    <li>Included form interaction CSS/JS files</li>
                    <li>Applied proper event handling to prevent sidebar conflicts</li>
                    <li>Enhanced accessibility with ARIA labels</li>
                </ul>
            </div>

            <!-- Test Section 1: Profile Edit Form -->
            <div class="test-section">
                <h2 class="test-subtitle">1. Profile Edit Form Test (/user/profile/edit)</h2>
                <p class="test-description">Test the "Nama Lengkap" and "Email" input fields that were previously blocked.</p>
                
                <div class="test-card">
                    <h3>Profile Edit Form Simulation</h3>
                    <form class="user-dashboard-form-container-enhanced" onsubmit="return testFormSubmit(event, 'Profile Edit')">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                            <!-- Name Input -->
                            <div class="user-dashboard-form-group user-dashboard-form-group-enhanced">
                                <label for="profile-name" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Nama Lengkap</label>
                                <input type="text"
                                       id="profile-name"
                                       name="name"
                                       value="John Doe"
                                       class="user-dashboard-form-input user-dashboard-form-input-enhanced"
                                       required
                                       onchange="logInteraction('Profile Name Input', this.value)"
                                       onfocus="logInteraction('Profile Name Input', 'focused')"
                                       onblur="logInteraction('Profile Name Input', 'blurred')">
                            </div>

                            <!-- Email Input -->
                            <div class="user-dashboard-form-group user-dashboard-form-group-enhanced">
                                <label for="profile-email" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Email</label>
                                <input type="email"
                                       id="profile-email"
                                       name="email"
                                       value="<EMAIL>"
                                       class="user-dashboard-form-input user-dashboard-form-input-enhanced"
                                       required
                                       onchange="logInteraction('Profile Email Input', this.value)"
                                       onfocus="logInteraction('Profile Email Input', 'focused')"
                                       onblur="logInteraction('Profile Email Input', 'blurred')">
                            </div>
                        </div>

                        <!-- Timezone Selection (already working) -->
                        <div class="user-dashboard-form-group user-dashboard-form-group-enhanced">
                            <label for="profile-timezone" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Zona Waktu</label>
                            <select id="profile-timezone" name="timezone" class="user-dashboard-form-select user-dashboard-form-select-enhanced" onchange="logInteraction('Profile Timezone', this.value)">
                                <option value="Asia/Jakarta">WIB - Waktu Indonesia Barat (UTC+7)</option>
                                <option value="Asia/Makassar">WITA - Waktu Indonesia Tengah (UTC+8)</option>
                                <option value="Asia/Jayapura">WIT - Waktu Indonesia Timur (UTC+9)</option>
                            </select>
                        </div>

                        <!-- Submit Button -->
                        <div class="user-dashboard-form-button-group-enhanced">
                            <button type="button" class="user-dashboard-form-cancel-enhanced" onclick="logInteraction('Profile Cancel Button', 'clicked')">
                                Batal
                            </button>
                            <button type="submit" class="user-dashboard-form-submit-enhanced">
                                Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Test Section 2: Settings Form -->
            <div class="test-section">
                <h2 class="test-subtitle">2. Settings Form Test (/user/settings)</h2>
                <p class="test-description">Test the profile and password forms that were previously blocked.</p>
                
                <div class="test-grid">
                    <!-- Mobile Navigation Test -->
                    <div class="test-card">
                        <h3>Mobile Navigation Dropdown</h3>
                        <div class="user-dashboard-dropdown-container-enhanced">
                            <label for="mobile-nav-test" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Pilih Bagian Pengaturan</label>
                            <select id="mobile-nav-test" class="user-dashboard-form-select user-dashboard-mobile-nav-enhanced" onchange="logInteraction('Mobile Navigation', this.value)">
                                <option value="profile">Profil & Zona Waktu</option>
                                <option value="password">Ubah Password</option>
                            </select>
                        </div>
                    </div>

                    <!-- Profile Form Test -->
                    <div class="test-card">
                        <h3>Profile Form</h3>
                        <form class="user-dashboard-form-container-enhanced" onsubmit="return testFormSubmit(event, 'Settings Profile')">
                            <div class="user-dashboard-form-group user-dashboard-form-group-enhanced">
                                <label for="settings-name" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Nama Lengkap</label>
                                <input type="text" id="settings-name" name="name" value="John Doe"
                                       class="user-dashboard-form-input user-dashboard-form-input-enhanced" required
                                       onchange="logInteraction('Settings Name Input', this.value)"
                                       onfocus="logInteraction('Settings Name Input', 'focused')"
                                       onblur="logInteraction('Settings Name Input', 'blurred')">
                            </div>

                            <div class="user-dashboard-form-group user-dashboard-form-group-enhanced">
                                <label for="settings-email" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Email</label>
                                <input type="email" id="settings-email" name="email" value="<EMAIL>"
                                       class="user-dashboard-form-input user-dashboard-form-input-enhanced" required
                                       onchange="logInteraction('Settings Email Input', this.value)"
                                       onfocus="logInteraction('Settings Email Input', 'focused')"
                                       onblur="logInteraction('Settings Email Input', 'blurred')">
                            </div>

                            <div class="user-dashboard-form-button-group-enhanced">
                                <button type="submit" class="user-dashboard-form-submit-enhanced">
                                    Simpan Perubahan
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Password Form Test -->
                    <div class="test-card">
                        <h3>Password Form</h3>
                        <form class="user-dashboard-form-container-enhanced" onsubmit="return testFormSubmit(event, 'Settings Password')">
                            <div class="user-dashboard-form-group user-dashboard-form-group-enhanced">
                                <label for="settings-password" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Password Baru</label>
                                <input type="password" id="settings-password" name="password"
                                       class="user-dashboard-form-input user-dashboard-form-password-enhanced" required
                                       onchange="logInteraction('Settings Password Input', '***')"
                                       onfocus="logInteraction('Settings Password Input', 'focused')"
                                       onblur="logInteraction('Settings Password Input', 'blurred')">
                            </div>

                            <div class="user-dashboard-form-group user-dashboard-form-group-enhanced">
                                <label for="settings-password-confirm" class="user-dashboard-form-label user-dashboard-form-label-enhanced">Konfirmasi Password Baru</label>
                                <input type="password" id="settings-password-confirm" name="password_confirmation"
                                       class="user-dashboard-form-input user-dashboard-form-password-enhanced" required
                                       onchange="logInteraction('Settings Password Confirm Input', '***')"
                                       onfocus="logInteraction('Settings Password Confirm Input', 'focused')"
                                       onblur="logInteraction('Settings Password Confirm Input', 'blurred')">
                            </div>

                            <div class="user-dashboard-form-button-group-enhanced">
                                <button type="submit" class="user-dashboard-form-submit-enhanced">
                                    Ubah Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="test-section">
                <h2 class="test-subtitle">Test Controls</h2>
                <button class="test-button" onclick="runSpecificTests()">Run Specific Tests</button>
                <button class="test-button" onclick="toggleSidebar()">Toggle Sidebar</button>
                <button class="test-button" onclick="enableDebugMode()">Enable Debug</button>
                <button class="test-button" onclick="clearResults()">Clear Results</button>
                <button class="test-button" onclick="testZIndexValues()">Test Z-Index Values</button>
            </div>

            <!-- Test Results -->
            <div class="test-section">
                <h2 class="test-subtitle">Interaction Log</h2>
                <div id="interaction-log" style="background: #f9fafb; padding: 1rem; border-radius: 0.5rem; min-height: 100px; font-family: monospace; font-size: 0.875rem;">
                    <p>Interaction log will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the form JavaScript -->
    <script src="js/user-dashboard-forms.js"></script>
    <script src="js/user-dashboard-dropdowns.js"></script>
    
    <!-- Test JavaScript -->
    <script>
        let interactionCount = 0;
        
        function toggleSidebar() {
            const sidebar = document.getElementById('mockSidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebar.classList.toggle('show');
            mainContent.classList.toggle('sidebar-open');
            
            logInteraction('Sidebar', sidebar.classList.contains('show') ? 'opened' : 'closed');
        }

        function enableDebugMode() {
            if (window.UserDashboardForms) {
                window.UserDashboardForms.enableDebug();
                logInteraction('Debug Mode', 'enabled');
            }
        }

        function clearResults() {
            document.getElementById('interaction-log').innerHTML = '<p>Interaction log cleared...</p>';
            interactionCount = 0;
        }

        function logInteraction(element, value) {
            interactionCount++;
            const log = document.getElementById('interaction-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${interactionCount}. ${element}: ${value}`;
            
            if (log.innerHTML.includes('Interaction log will appear here') || log.innerHTML.includes('cleared')) {
                log.innerHTML = `<p>${entry}</p>`;
            } else {
                log.innerHTML += `<p>${entry}</p>`;
            }
            
            log.scrollTop = log.scrollHeight;
        }

        function testFormSubmit(event, formType) {
            event.preventDefault();
            logInteraction(`${formType} Form`, 'submitted successfully');
            return false;
        }

        function testZIndexValues() {
            const elements = [
                { id: 'profile-name', name: 'Profile Name Input' },
                { id: 'profile-email', name: 'Profile Email Input' },
                { id: 'settings-name', name: 'Settings Name Input' },
                { id: 'settings-email', name: 'Settings Email Input' },
                { id: 'settings-password', name: 'Settings Password Input' },
                { id: 'settings-password-confirm', name: 'Settings Password Confirm Input' }
            ];
            
            elements.forEach(element => {
                const el = document.getElementById(element.id);
                if (el) {
                    const computedStyle = window.getComputedStyle(el);
                    logInteraction(`Z-index ${element.name}`, computedStyle.zIndex);
                }
            });
            
            const sidebar = document.getElementById('mockSidebar');
            const sidebarStyle = window.getComputedStyle(sidebar);
            logInteraction('Z-index Sidebar', sidebarStyle.zIndex);
        }

        function runSpecificTests() {
            logInteraction('Specific Test Suite', 'started');
            
            // Test all problematic inputs
            const problemInputs = [
                'profile-name', 'profile-email', 'settings-name', 
                'settings-email', 'settings-password', 'settings-password-confirm'
            ];
            
            problemInputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.focus();
                    setTimeout(() => element.blur(), 100);
                    logInteraction(`${id} focus/blur`, 'tested');
                }
            });
            
            // Test z-index values
            testZIndexValues();
            
            logInteraction('Specific Test Suite', 'completed');
        }

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                logInteraction('Page', 'loaded successfully');
                logInteraction('Form Enhancement', 'initialized');
                // Auto-open sidebar for testing
                toggleSidebar();
                logInteraction('Test Setup', 'sidebar opened for testing');
            }, 1000);
        });
    </script>
</body>
</html>
