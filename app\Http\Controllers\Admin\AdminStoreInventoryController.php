<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\Product;
use App\Models\StoreStock;
use App\Models\Distribution;
use App\Models\StockMovement;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AdminStoreInventoryController extends Controller
{
    /**
     * Display store inventory management page
     */
    public function index(Request $request)
    {
        // Get all stores with users for dropdown
        $stores = User::where('role', 'user')
            ->with('store')
            ->whereHas('store')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->store->id,
                    'name' => $user->store->name,
                    'location' => $user->store->location,
                    'user_name' => $user->name
                ];
            })
            ->sortBy('name')
            ->values();

        $selectedStore = null;
        $storeProducts = collect();
        $storeStats = [];

        // If store is selected, get its inventory data
        if ($request->filled('store_id')) {
            $selectedStore = Store::find($request->get('store_id'));
            
            if ($selectedStore) {
                // Get products that have been distributed to this store
                $storeProducts = $this->getStoreProducts($selectedStore->id, $request);
                $storeStats = $this->getStoreStats($selectedStore->id);
            }
        }

        return view('admin.store-inventory.index', compact(
            'stores',
            'selectedStore',
            'storeProducts',
            'storeStats'
        ));
    }

    /**
     * Get products for a specific store with stock and distribution data
     */
    private function getStoreProducts($storeId, Request $request)
    {
        // Get all products that have been distributed to this store
        $query = Product::whereHas('distributions', function ($q) use ($storeId) {
            $q->where('store_id', $storeId);
        })->with([
            'storeStock' => function ($q) use ($storeId) {
                $q->where('store_id', $storeId);
            },
            'distributions' => function ($q) use ($storeId) {
                $q->where('store_id', $storeId)
                  ->orderBy('date_distributed', 'desc');
            }
        ]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('name', 'like', "%{$search}%");
        }

        $products = $query->orderBy('name')->get();

        // Transform products to include additional data
        return $products->map(function ($product) use ($storeId) {
            $storeStock = $product->storeStock->first();
            $currentStock = $storeStock ? $storeStock->quantity : 0;
            
            // Get distribution statistics
            $totalDistributed = $product->distributions->sum('quantity');
            $totalReceived = $product->distributions->where('confirmed', true)->sum('received_quantity');
            $pendingDistributions = $product->distributions->where('confirmed', false)->count();
            
            // Get last distribution
            $lastDistribution = $product->distributions->first();
            
            return [
                'id' => $product->id,
                'name' => $product->name,
                'current_stock' => $currentStock,
                'total_distributed' => $totalDistributed,
                'total_received' => $totalReceived,
                'pending_distributions' => $pendingDistributions,
                'last_distribution' => $lastDistribution,
                'stock_status' => $this->getStockStatus($currentStock),
                'store_stock_id' => $storeStock ? $storeStock->id : null
            ];
        });
    }

    /**
     * Get statistics for a specific store
     */
    private function getStoreStats($storeId)
    {
        $totalProducts = Product::whereHas('distributions', function ($q) use ($storeId) {
            $q->where('store_id', $storeId);
        })->count();

        $totalStock = StoreStock::where('store_id', $storeId)->sum('quantity');
        
        $pendingDistributions = Distribution::where('store_id', $storeId)
            ->where('confirmed', false)
            ->count();
            
        $totalDistributions = Distribution::where('store_id', $storeId)->count();

        return [
            'total_products' => $totalProducts,
            'total_stock' => $totalStock,
            'pending_distributions' => $pendingDistributions,
            'total_distributions' => $totalDistributions
        ];
    }

    /**
     * Get stock status based on quantity
     */
    private function getStockStatus($quantity)
    {
        if ($quantity <= 0) {
            return ['status' => 'out', 'label' => 'Habis', 'class' => 'bg-red-100 text-red-800'];
        } elseif ($quantity <= 5) {
            return ['status' => 'low', 'label' => 'Sedikit', 'class' => 'bg-yellow-100 text-yellow-800'];
        } else {
            return ['status' => 'good', 'label' => 'Baik', 'class' => 'bg-green-100 text-green-800'];
        }
    }

    /**
     * Show stock adjustment form for a specific product at a store
     */
    public function adjustForm(Request $request)
    {
        $storeId = $request->get('store_id');
        $productId = $request->get('product_id');
        
        $store = Store::findOrFail($storeId);
        $product = Product::findOrFail($productId);
        
        // Get current store stock
        $storeStock = StoreStock::where('store_id', $storeId)
            ->where('product_id', $productId)
            ->first();
            
        $currentStock = $storeStock ? $storeStock->quantity : 0;
        
        // Get recent distribution history
        $recentDistributions = Distribution::where('store_id', $storeId)
            ->where('product_id', $productId)
            ->with('product')
            ->orderBy('date_distributed', 'desc')
            ->limit(5)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'store' => $store,
                'product' => $product,
                'current_stock' => $currentStock,
                'recent_distributions' => $recentDistributions->map(function ($dist) {
                    return [
                        'id' => $dist->id,
                        'quantity' => $dist->quantity,
                        'received_quantity' => $dist->received_quantity,
                        'date_distributed' => $dist->date_distributed->format('d M Y'),
                        'confirmed' => $dist->confirmed,
                        'status_label' => $dist->status_label
                    ];
                })
            ]
        ]);
    }

    /**
     * Process stock adjustment for a specific product at a store
     */
    public function adjustStock(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'store_id' => 'required|exists:stores,id',
            'product_id' => 'required|exists:products,id',
            'adjustment_type' => 'required|in:add,subtract,set',
            'quantity' => 'required|integer|min:0',
            'notes' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $validator->errors()
            ], 422);
        }

        $storeId = $request->get('store_id');
        $productId = $request->get('product_id');
        $adjustmentType = $request->get('adjustment_type');
        $quantity = $request->get('quantity');
        $notes = $request->get('notes', '');

        $store = Store::findOrFail($storeId);
        $product = Product::findOrFail($productId);

        try {
            DB::transaction(function () use ($store, $product, $adjustmentType, $quantity, $notes) {
                // Find or create store stock entry
                $storeStock = StoreStock::firstOrCreate(
                    [
                        'store_id' => $store->id,
                        'product_id' => $product->id
                    ],
                    ['quantity' => 0]
                );

                $previousStock = $storeStock->quantity;
                $newStock = $previousStock;

                // Calculate new stock based on adjustment type
                switch ($adjustmentType) {
                    case 'add':
                        $newStock = $previousStock + $quantity;
                        break;
                    case 'subtract':
                        $newStock = max(0, $previousStock - $quantity);
                        break;
                    case 'set':
                        $newStock = $quantity;
                        break;
                }

                // Update store stock
                $storeStock->update(['quantity' => $newStock]);

                // Log stock movement
                StockMovement::create([
                    'product_id' => $product->id,
                    'type' => $newStock > $previousStock ? 'in' : 'out',
                    'source' => 'store_adjustment',
                    'quantity' => $newStock - $previousStock,
                    'previous_stock' => $previousStock,
                    'new_stock' => $newStock,
                    'reference_type' => 'store',
                    'reference_id' => $store->id,
                    'notes' => $notes ?: "Penyesuaian stok toko {$store->name} oleh admin",
                    'created_by' => auth()->id(),
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => 'Stok berhasil disesuaikan'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyesuaikan stok: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get distribution history for a specific product at a store
     */
    public function getDistributionHistory(Request $request)
    {
        $storeId = $request->get('store_id');
        $productId = $request->get('product_id');
        
        $distributions = Distribution::where('store_id', $storeId)
            ->where('product_id', $productId)
            ->with(['product', 'store'])
            ->orderBy('date_distributed', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $distributions->items(),
            'pagination' => [
                'current_page' => $distributions->currentPage(),
                'last_page' => $distributions->lastPage(),
                'per_page' => $distributions->perPage(),
                'total' => $distributions->total()
            ]
        ]);
    }
}
