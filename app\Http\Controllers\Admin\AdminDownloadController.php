<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Store;
use App\Models\Distribution;
use App\Models\User;
use App\Models\WarehouseStock;
use App\Services\ExcelExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Carbon\Carbon;

class AdminDownloadController extends Controller
{
    /**
     * Show the download form
     */
    public function showForm()
    {
        // Get statistics for the download page
        $stats = [
            'total_products' => Product::count(),
            'total_stores' => Store::count(),
            'total_distributions' => Distribution::count(),
            'recent_downloads' => Distribution::whereDate('created_at', '>=', now()->subDays(7))->count(),
        ];

        return view('admin.download.form', compact('stats'));
    }

    /**
     * Process the download request
     */
    public function download(Request $request, ExcelExportService $excelService)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $startDate = Carbon::parse($request->start_date)->startOfDay();
        $endDate = Carbon::parse($request->end_date)->endOfDay();

        // Get user's timezone
        $userTimezone = auth()->user()->timezone ?? 'Asia/Jakarta';

        // Prepare worksheets data
        $worksheets = $this->prepareWorksheetData($startDate, $endDate, $userTimezone, $excelService);

        // Create filename
        $filename = $excelService->createFilename('data_export', $startDate, $endDate);

        return $excelService->generateExcelFile($worksheets, $filename, $userTimezone);
    }

    /**
     * Prepare worksheet data for Excel export
     */
    private function prepareWorksheetData($startDate, $endDate, $userTimezone, $excelService)
    {
        $worksheets = [];

        // Products worksheet
        $worksheets[] = $this->prepareProductsWorksheet($excelService, $userTimezone);

        // Stores worksheet
        $worksheets[] = $this->prepareStoresWorksheet($excelService, $userTimezone);

        // Distributions worksheet
        $worksheets[] = $this->prepareDistributionsWorksheet($startDate, $endDate, $excelService, $userTimezone);

        return $worksheets;
    }

    /**
     * Prepare products worksheet data
     */
    private function prepareProductsWorksheet($excelService, $userTimezone)
    {
        $products = Product::with(['warehouseStock', 'distributions', 'storeStock'])->get();

        $productData = [];
        $no = 1;
        foreach ($products as $product) {
            $warehouseStock = $product->warehouseStock->sum('quantity');
            $totalDistributions = $product->distributions()->count();
            $storeStock = $product->storeStock->sum('quantity');
            $createdAt = $excelService->formatDate($product->created_at, $userTimezone);

            $productData[] = [
                $no++,
                $product->name,
                $warehouseStock,
                $totalDistributions,
                $storeStock,
                $createdAt
            ];
        }

        return [
            'name' => 'Data Produk',
            'title' => 'DATA PRODUK - INDAH BERKAH ABADI',
            'headers' => ['No', 'Nama Produk', 'Stok Gudang', 'Total Distribusi', 'Stok Toko', 'Dibuat Pada'],
            'data' => $productData
        ];
    }

    /**
     * Prepare stores worksheet data
     */
    private function prepareStoresWorksheet($excelService, $userTimezone)
    {
        $stores = Store::with(['users', 'distributions'])->get();

        $storeData = [];
        $no = 1;
        foreach ($stores as $store) {
            $userCount = $store->users()->count();
            $distributionCount = $store->distributions()->count();
            $createdAt = $excelService->formatDate($store->created_at, $userTimezone);

            $storeData[] = [
                $no++,
                $store->name,
                $store->location,
                $userCount,
                $distributionCount,
                $createdAt
            ];
        }

        return [
            'name' => 'Data Toko',
            'title' => 'DATA TOKO - INDAH BERKAH ABADI',
            'headers' => ['No', 'Nama Toko', 'Lokasi', 'Jumlah Pengguna', 'Total Distribusi', 'Dibuat Pada'],
            'data' => $storeData
        ];
    }

    /**
     * Prepare distributions worksheet data
     */
    private function prepareDistributionsWorksheet($startDate, $endDate, $excelService, $userTimezone)
    {
        $distributions = Distribution::with(['product', 'store'])
            ->whereBetween('date_distributed', [$startDate, $endDate])
            ->orderBy('date_distributed', 'desc')
            ->get();

        $distributionData = [];
        $no = 1;
        foreach ($distributions as $distribution) {
            $dateDistributed = Carbon::parse($distribution->date_distributed)->format('d/m/Y');
            $productName = $distribution->product ? $distribution->product->name : 'N/A';
            $storeName = $distribution->store ? $distribution->store->name : 'N/A';
            $status = $distribution->confirmed ? 'Dikonfirmasi' : 'Belum Dikonfirmasi';
            $notes = $distribution->notes ?? '';
            $createdAt = $excelService->formatDate($distribution->created_at, $userTimezone);

            $distributionData[] = [
                $no++,
                $dateDistributed,
                $productName,
                $storeName,
                $distribution->quantity,
                $distribution->received_quantity ?? 0,
                $status,
                $notes,
                $createdAt
            ];
        }

        return [
            'name' => 'Data Distribusi',
            'title' => 'DATA DISTRIBUSI - INDAH BERKAH ABADI',
            'subtitle' => 'Periode: ' . $startDate->format('d/m/Y') . ' - ' . $endDate->format('d/m/Y'),
            'headers' => ['No', 'Tanggal Distribusi', 'Produk', 'Toko', 'Jumlah', 'Jumlah Diterima', 'Status', 'Catatan', 'Dibuat Pada'],
            'data' => $distributionData
        ];
    }
}
