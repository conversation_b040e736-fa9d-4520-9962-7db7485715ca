/**
 * Admin Product Inventory Management JavaScript
 * PT. Indah Berkah Abadi - Inventory System
 */

// Global variables
let currentStoreId = null;
let currentProductId = null;
let currentStoreName = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeProductInventory();
});

/**
 * Initialize product inventory functionality
 */
function initializeProductInventory() {
    // Set up CSRF token for AJAX requests
    const token = document.querySelector('meta[name="csrf-token"]');
    if (token) {
        window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.getAttribute('content');
    }

    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('admin-dashboard-modal')) {
            closeAllModals();
        }
    });

    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
}

/**
 * Open stock adjustment modal
 */
function openStockAdjustModal(storeId, productId, productName, currentStock, storeName) {
    currentStoreId = storeId;
    currentProductId = productId;
    currentStoreName = storeName;

    // Set form values
    document.getElementById('adjust_store_id').value = storeId;
    document.getElementById('adjust_product_id').value = productId;
    document.getElementById('adjust_product_name').textContent = productName;
    document.getElementById('adjust_store_name').textContent = storeName;
    document.getElementById('adjust_current_stock').textContent = currentStock + ' unit';

    // Reset form
    document.getElementById('stockAdjustForm').reset();
    document.getElementById('adjust_store_id').value = storeId;
    document.getElementById('adjust_product_id').value = productId;

    // Show modal
    document.getElementById('stockAdjustModal').classList.add('active');
}

/**
 * Close stock adjustment modal
 */
function closeStockAdjustModal() {
    document.getElementById('stockAdjustModal').classList.remove('active');
    currentStoreId = null;
    currentProductId = null;
    currentStoreName = null;
}

/**
 * Submit stock adjustment
 */
function submitStockAdjustment() {
    const form = document.getElementById('stockAdjustForm');
    const formData = new FormData(form);

    // Validate form
    const adjustmentType = formData.get('adjustment_type');
    const quantity = parseInt(formData.get('quantity'));

    if (!adjustmentType) {
        showNotification('Pilih jenis penyesuaian', 'error');
        return;
    }

    if (!quantity || quantity < 0) {
        showNotification('Masukkan jumlah yang valid', 'error');
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('#stockAdjustModal .admin-dashboard-btn-primary');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Memproses...';
    submitBtn.disabled = true;

    // Submit via AJAX
    fetch('/admin/product-inventory/adjust', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            closeStockAdjustModal();
            // Reload page to show updated data
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            if (data.errors) {
                // Handle validation errors
                let errorMessage = 'Validasi gagal:\n';
                for (const [field, messages] of Object.entries(data.errors)) {
                    errorMessage += `- ${messages.join(', ')}\n`;
                }
                showNotification(errorMessage, 'error');
            } else {
                showNotification(data.message || 'Terjadi kesalahan', 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Terjadi kesalahan saat memproses permintaan', 'error');
    })
    .finally(() => {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

/**
 * View distribution history - enhanced version
 */
function viewDistributionHistory(storeId, productId, productName, storeName) {
    // Set modal title
    document.querySelector('#distributionHistoryModal .admin-dashboard-modal-title').textContent =
        `Riwayat Distribusi - ${productName} (${storeName})`;

    // Show enhanced loading
    const content = document.getElementById('distributionHistoryContent');
    content.innerHTML = `
        <div class="admin-dashboard-loading">
            <div class="flex items-center justify-center mb-4">
                <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <p class="text-center text-gray-600 font-medium">Memuat riwayat distribusi...</p>
        </div>
    `;

    // Show modal with enhanced styling
    const modal = document.getElementById('distributionHistoryModal');
    modal.classList.add('active', 'admin-dashboard-modal-enhanced');

    // Load distribution history
    fetch(`/admin/product-inventory/distribution-history?store_id=${storeId}&product_id=${productId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderDistributionHistory(data.data);
        } else {
            content.innerHTML = `
                <div class="admin-dashboard-error-state">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <p class="text-red-800 font-semibold">Gagal memuat riwayat distribusi</p>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        content.innerHTML = `
            <div class="admin-dashboard-error-state">
                <div class="text-center">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-red-800 font-semibold">Terjadi kesalahan saat memuat data</p>
                </div>
            </div>
        `;
    });
}

/**
 * Render distribution history - enhanced version
 */
function renderDistributionHistory(distributions) {
    const content = document.getElementById('distributionHistoryContent');

    if (distributions.length === 0) {
        content.innerHTML = `
            <div class="admin-dashboard-empty-state">
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Belum Ada Riwayat</h3>
                    <p class="text-gray-600">Produk ini belum pernah didistribusikan ke toko ini.</p>
                </div>
            </div>
        `;
        return;
    }

    // Calculate summary statistics
    const totalDistributed = distributions.reduce((sum, dist) => sum + parseInt(dist.quantity), 0);
    const totalReceived = distributions.reduce((sum, dist) => sum + parseInt(dist.received_quantity || 0), 0);
    const confirmedCount = distributions.filter(dist => dist.confirmed).length;

    let html = `
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-blue-700">Total Distribusi</p>
                        <p class="text-lg font-bold text-blue-900">${totalDistributed.toLocaleString()}</p>
                    </div>
                </div>
            </div>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-green-700">Total Diterima</p>
                        <p class="text-lg font-bold text-green-900">${totalReceived.toLocaleString()}</p>
                    </div>
                </div>
            </div>
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-purple-700">Dikonfirmasi</p>
                        <p class="text-lg font-bold text-purple-900">${confirmedCount}/${distributions.length}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribution Table -->
        <div class="admin-dashboard-table-container">
            <table class="admin-dashboard-table">
                <thead class="admin-dashboard-table-header">
                    <tr>
                        <th class="admin-dashboard-table-th">Tanggal</th>
                        <th class="admin-dashboard-table-th">Jumlah Kirim</th>
                        <th class="admin-dashboard-table-th">Jumlah Terima</th>
                        <th class="admin-dashboard-table-th">Status</th>
                    </tr>
                </thead>
                <tbody class="admin-dashboard-table-body">
    `;

    distributions.forEach(dist => {
        const statusClass = dist.confirmed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
        const statusText = dist.confirmed ? 'Dikonfirmasi' : 'Belum Dikonfirmasi';

        html += `
            <tr class="admin-dashboard-table-row">
                <td class="admin-dashboard-table-td">
                    <span class="font-medium">${formatDate(dist.date_distributed)}</span>
                </td>
                <td class="admin-dashboard-table-td">
                    <span class="font-semibold text-blue-700">${parseInt(dist.quantity).toLocaleString()}</span>
                    <span class="text-sm text-gray-500 ml-1">unit</span>
                </td>
                <td class="admin-dashboard-table-td">
                    ${dist.received_quantity ? `
                        <span class="font-semibold text-green-700">${parseInt(dist.received_quantity).toLocaleString()}</span>
                        <span class="text-sm text-gray-500 ml-1">unit</span>
                    ` : `
                        <span class="text-gray-400 italic">Belum dikonfirmasi</span>
                    `}
                </td>
                <td class="admin-dashboard-table-td">
                    <span class="admin-dashboard-badge ${statusClass}">${statusText}</span>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    content.innerHTML = html;
}

/**
 * Close distribution history modal
 */
function closeDistributionHistoryModal() {
    const modal = document.getElementById('distributionHistoryModal');
    modal.classList.remove('active', 'admin-dashboard-modal-enhanced');
}

/**
 * Close all modals
 */
function closeAllModals() {
    closeStockAdjustModal();
    closeDistributionHistoryModal();
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.admin-dashboard-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `admin-dashboard-notification admin-dashboard-notification-${type}`;
    
    const iconSvg = type === 'success' 
        ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>'
        : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';

    notification.innerHTML = `
        <div class="admin-dashboard-notification-content">
            <svg class="admin-dashboard-notification-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                ${iconSvg}
            </svg>
            <span>${message}</span>
        </div>
        <button class="admin-dashboard-notification-close" onclick="this.parentElement.remove()">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    };
    return date.toLocaleDateString('id-ID', options);
}

/**
 * Handle form submission with Enter key
 */
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.target.closest('#stockAdjustForm')) {
        e.preventDefault();
        submitStockAdjustment();
    }
});
