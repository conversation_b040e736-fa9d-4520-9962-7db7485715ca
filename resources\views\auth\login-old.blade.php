@extends('layouts.app')

@section('title', 'Masuk - Indah Berkah Abadi')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <div class="text-center mb-8">
            <a href="{{ route('home') }}" class="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4 transition-colors">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali ke Beranda
            </a>
            <div class="flex items-center justify-center space-x-4 mb-4">
                <div class="h-12 w-12 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white font-bold text-lg">IBA</span>
                </div>
                <h1 class="text-2xl font-bold text-gray-900">Indah Berkah Abadi</h1>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Masuk Sistem</h2>
                <p class="card-description">Akses dashboard manajemen logistik Anda</p>
            </div>
            <div class="card-content">
                @if(session('success'))
                    <div class="alert alert-success mb-4">
                        {{ session('success') }}
                    </div>
                @endif

                @if(session('info'))
                    <div class="alert alert-info mb-4">
                        {{ session('info') }}
                    </div>
                @endif

                <form method="POST" action="{{ route('login.post') }}" class="space-y-4">
                    @csrf
                    
                    <div class="form-group">
                        <label for="email" class="form-label">Email</label>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            class="form-input @error('email') form-input-error @enderror"
                            placeholder="Masukkan email Anda"
                            value="{{ old('email') }}"
                            required
                        >
                        @error('email')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Kata Sandi</label>
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            class="form-input @error('password') form-input-error @enderror"
                            placeholder="Masukkan kata sandi"
                            required
                        >
                        @error('password')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>



                    <div class="form-group">
                        <label class="flex items-center">
                            <input type="checkbox" name="remember" class="form-checkbox">
                            <span class="ml-2 text-sm text-gray-600">Ingat saya</span>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary w-full">
                        Masuk
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
