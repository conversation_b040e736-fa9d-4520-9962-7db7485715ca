<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Supplier;

class SupplierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample suppliers
        Supplier::firstOrCreate(
            ['name' => 'PT. Sumber Makmur'],
            [
                'contact_person' => 'Budi Santoso',
                'phone' => '021-12345678',
                'email' => '<EMAIL>',
                'address' => 'Jl. Industri No. 123, Jakarta Timur',
                'status' => 'active',
            ]
        );

        Supplier::firstOrCreate(
            ['name' => 'CV. Berkah Jaya'],
            [
                'contact_person' => 'Siti <PERSON>hayu',
                'phone' => '031-87654321',
                'email' => '<EMAIL>',
                'address' => 'Jl. Perdagangan No. 456, Surabaya',
                'status' => 'active',
            ]
        );

        Supplier::firstOrCreate(
            ['name' => 'UD. Maj<PERSON>'],
            [
                'contact_person' => '<PERSON> W<PERSON>',
                'phone' => '0274-555666',
                'email' => '<EMAIL>',
                'address' => 'Jl. Malioboro No. 789, Yogyakarta',
                'status' => 'active',
            ]
        );

        Supplier::firstOrCreate(
            ['name' => 'PT. Global Supply'],
            [
                'contact_person' => 'Maria Gonzales',
                'phone' => '022-999888',
                'email' => '<EMAIL>',
                'address' => 'Jl. Asia Afrika No. 321, Bandung',
                'status' => 'active',
            ]
        );

        Supplier::firstOrCreate(
            ['name' => 'CV. Nusantara Prima'],
            [
                'contact_person' => 'Joko Susilo',
                'phone' => '0542-777444',
                'email' => '<EMAIL>',
                'address' => 'Jl. Soekarno Hatta No. 654, Balikpapan',
                'status' => 'active',
            ]
        );

        // Create one inactive supplier for testing
        Supplier::firstOrCreate(
            ['name' => 'PT. Lama Jaya'],
            [
                'contact_person' => 'Bambang Sutrisno',
                'phone' => '0361-111222',
                'email' => '<EMAIL>',
                'address' => 'Jl. Sunset Road No. 987, Denpasar',
                'status' => 'inactive',
            ]
        );
    }
}
