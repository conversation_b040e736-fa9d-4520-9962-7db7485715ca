/* 
 * Independent CSS for User Dashboard Product Management
 * PT. Indah Berkah Abadi - Inventory System
 * Prevents conflicts with other CSS frameworks and ensures proper z-index hierarchy
 * 
 * Z-INDEX HIERARCHY:
 * - Sidebar navigation: 1000+ (highest priority)
 * - Product management modals: 100-999 (high priority)
 * - Product management interactive elements: 10-99 (medium priority)
 * - Product management containers: 5 (container level)
 * - Main content: 1 (lowest priority)
 */

/* ===== STATISTICS CARDS ===== */

.user-dashboard-product-stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
    margin-bottom: 2rem !important;
    position: relative !important;
    z-index: 5 !important;
}

.user-dashboard-product-stat-card {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.15s ease-in-out !important;
    position: relative !important;
    z-index: 10 !important;
}

.user-dashboard-product-stat-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-1px) !important;
}

.user-dashboard-product-stat-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: 0.75rem !important;
}

.user-dashboard-product-stat-title {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #6b7280 !important;
    margin: 0 !important;
    line-height: 1.25 !important;
}

.user-dashboard-product-stat-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #9ca3af !important;
    flex-shrink: 0 !important;
}

.user-dashboard-product-stat-value {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    line-height: 1.2 !important;
    margin-bottom: 0.25rem !important;
}

.user-dashboard-product-stat-description {
    font-size: 0.75rem !important;
    color: #6b7280 !important;
    line-height: 1.25 !important;
}

/* Status-specific styling */
.user-dashboard-product-stat-warning {
    border-left: 4px solid #f59e0b !important;
}

.user-dashboard-product-stat-warning .user-dashboard-product-stat-icon {
    color: #f59e0b !important;
}

.user-dashboard-product-stat-danger {
    border-left: 4px solid #ef4444 !important;
}

.user-dashboard-product-stat-danger .user-dashboard-product-stat-icon {
    color: #ef4444 !important;
}

/* ===== SEARCH AND FILTERS ===== */

.user-dashboard-product-search-wrapper {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
    position: relative !important;
    z-index: 10 !important;
}

.user-dashboard-product-search-form {
    display: flex !important;
    gap: 1rem !important;
    align-items: end !important;
    flex-wrap: wrap !important;
}

.user-dashboard-product-input-group {
    flex: 1 !important;
    min-width: 200px !important;
    position: relative !important;
    z-index: 15 !important;
}

.user-dashboard-product-label {
    display: block !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.5 !important;
}

.user-dashboard-product-icon {
    width: 1rem !important;
    height: 1rem !important;
    margin-right: 0.5rem !important;
}

/* ===== PRODUCT TABLE ===== */

.user-dashboard-product-table-wrapper {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    overflow: hidden !important;
    position: relative !important;
    z-index: 5 !important;
}

.user-dashboard-product-table-container {
    overflow-x: auto !important;
    position: relative !important;
    z-index: 10 !important;
}

.user-dashboard-product-table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-size: 0.875rem !important;
}

.user-dashboard-product-table-header {
    background-color: #f9fafb !important;
    border-bottom: 1px solid #e5e7eb !important;
}

.user-dashboard-product-table-th {
    padding: 0.75rem !important;
    text-align: left !important;
    font-weight: 600 !important;
    color: #374151 !important;
    white-space: nowrap !important;
}

.user-dashboard-product-table-body {
    background-color: #ffffff !important;
}

.user-dashboard-product-table-row {
    border-bottom: 1px solid #f3f4f6 !important;
    transition: background-color 0.15s ease-in-out !important;
}

.user-dashboard-product-table-row:hover {
    background-color: #f9fafb !important;
}

.user-dashboard-product-table-td {
    padding: 0.75rem !important;
    vertical-align: top !important;
}

/* ===== PRODUCT INFO ===== */

.user-dashboard-product-info {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.25rem !important;
}

.user-dashboard-product-name {
    font-weight: 600 !important;
    color: #111827 !important;
    line-height: 1.25 !important;
}

.user-dashboard-product-meta {
    font-size: 0.75rem !important;
    color: #6b7280 !important;
    line-height: 1.25 !important;
}

.user-dashboard-product-quantity {
    font-weight: 600 !important;
    color: #111827 !important;
    font-size: 0.875rem !important;
}

/* ===== STATUS BADGES ===== */

.user-dashboard-product-status {
    display: inline-flex !important;
    align-items: center !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 0.375rem !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.25 !important;
}

.user-dashboard-product-status-success {
    background-color: #d1fae5 !important;
    color: #065f46 !important;
}

.user-dashboard-product-status-warning {
    background-color: #fef3c7 !important;
    color: #92400e !important;
}

.user-dashboard-product-status-danger {
    background-color: #fee2e2 !important;
    color: #991b1b !important;
}

/* ===== ACTION BUTTONS ===== */

.user-dashboard-product-actions {
    display: flex !important;
    gap: 0.5rem !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    position: relative !important;
    z-index: 20 !important;
}

.user-dashboard-product-btn {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 36px !important;
    min-width: 36px !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.25 !important;
    border-radius: 0.375rem !important;
    cursor: pointer !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    touch-action: manipulation !important;
    box-sizing: border-box !important;
    text-decoration: none !important;
    user-select: none !important;
    border: 1px solid transparent !important;
    position: relative !important;
    z-index: 25 !important;
}

.user-dashboard-product-btn-primary {
    color: #ffffff !important;
    background-color: #166534 !important;
    border-color: #166534 !important;
}

.user-dashboard-product-btn-primary:hover {
    background-color: #15803d !important;
    border-color: #15803d !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(22, 101, 52, 0.2) !important;
}

.user-dashboard-product-btn-danger {
    color: #ffffff !important;
    background-color: #dc2626 !important;
    border-color: #dc2626 !important;
}

.user-dashboard-product-btn-danger:hover {
    background-color: #b91c1c !important;
    border-color: #b91c1c !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2) !important;
}

.user-dashboard-product-btn-icon {
    width: 0.875rem !important;
    height: 0.875rem !important;
    margin-right: 0.25rem !important;
    flex-shrink: 0 !important;
}

.user-dashboard-product-quick-form {
    display: inline-block !important;
    position: relative !important;
    z-index: 25 !important;
}

.user-dashboard-product-no-action {
    font-size: 0.75rem !important;
    color: #6b7280 !important;
    font-style: italic !important;
}

/* ===== CURRENT STOCK DISPLAY ===== */

.user-dashboard-product-current-stock {
    margin-bottom: 2rem !important;
    position: relative !important;
    z-index: 5 !important;
}

.user-dashboard-product-stock-card {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    padding: 1.5rem !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    text-align: center !important;
}

.user-dashboard-product-stock-header {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    margin-bottom: 1rem !important;
}

.user-dashboard-product-stock-title {
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin: 0 !important;
}

.user-dashboard-product-stock-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #6b7280 !important;
}

.user-dashboard-product-stock-value {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    line-height: 1.2 !important;
    margin-bottom: 0.5rem !important;
}

.user-dashboard-product-stock-description {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    line-height: 1.25 !important;
}

/* ===== FORM WRAPPER ===== */

.user-dashboard-product-form-wrapper {
    background-color: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    padding: 1.5rem !important;
    position: relative !important;
    z-index: 10 !important;
}

.user-dashboard-product-form {
    position: relative !important;
    z-index: 15 !important;
}

.user-dashboard-product-quantity-hint {
    font-size: 0.75rem !important;
    color: #6b7280 !important;
    margin-top: 0.25rem !important;
    line-height: 1.25 !important;
}

/* ===== PREVIEW SECTION ===== */

.user-dashboard-product-preview {
    background-color: #f9fafb !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    margin-top: 1rem !important;
    position: relative !important;
    z-index: 15 !important;
}

.user-dashboard-product-preview-header {
    margin-bottom: 0.75rem !important;
}

.user-dashboard-product-preview-title {
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin: 0 !important;
}

.user-dashboard-product-preview-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
}

.user-dashboard-product-preview-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.user-dashboard-product-preview-label {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
}

.user-dashboard-product-preview-value {
    font-size: 0.875rem !important;
    color: #111827 !important;
    font-weight: 600 !important;
}

.user-dashboard-product-change-negative {
    color: #dc2626 !important;
}

.user-dashboard-product-stock-zero {
    color: #ef4444 !important;
}

.user-dashboard-product-preview-result {
    border-top: 1px solid #d1d5db !important;
    padding-top: 0.5rem !important;
    margin-top: 0.5rem !important;
}

/* ===== VALIDATION WARNING ===== */

.user-dashboard-product-validation-warning {
    background-color: #fef3c7 !important;
    border: 1px solid #f59e0b !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    margin-top: 1rem !important;
    display: flex !important;
    gap: 0.75rem !important;
    position: relative !important;
    z-index: 15 !important;
}

.user-dashboard-product-warning-icon {
    flex-shrink: 0 !important;
}

.user-dashboard-product-warning-icon svg {
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #f59e0b !important;
}

.user-dashboard-product-warning-content {
    flex: 1 !important;
}

.user-dashboard-product-warning-title {
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #92400e !important;
    margin: 0 0 0.25rem 0 !important;
}

.user-dashboard-product-warning-message {
    font-size: 0.875rem !important;
    color: #92400e !important;
    line-height: 1.4 !important;
    margin: 0 !important;
}

/* ===== DISABLED SUBMIT BUTTON ===== */

.user-dashboard-form-submit-disabled {
    background-color: #f9fafb !important;
    color: #6b7280 !important;
    border-color: #d1d5db !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

.user-dashboard-form-submit-disabled:hover {
    background-color: #f9fafb !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== ERROR MESSAGES ===== */

.user-dashboard-product-error-container {
    background-color: #fee2e2 !important;
    border: 1px solid #fecaca !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
    position: relative !important;
    z-index: 15 !important;
}

.user-dashboard-product-error-header {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    margin-bottom: 0.75rem !important;
}

.user-dashboard-product-error-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #dc2626 !important;
    flex-shrink: 0 !important;
}

.user-dashboard-product-error-title {
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #991b1b !important;
    margin: 0 !important;
}

.user-dashboard-product-error-list {
    list-style: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.user-dashboard-product-error-item {
    font-size: 0.875rem !important;
    color: #991b1b !important;
    line-height: 1.4 !important;
    margin-bottom: 0.25rem !important;
    padding-left: 1.5rem !important;
    position: relative !important;
}

.user-dashboard-product-error-item:before {
    content: "•" !important;
    position: absolute !important;
    left: 0.5rem !important;
    color: #dc2626 !important;
    font-weight: bold !important;
}

/* ===== SUCCESS MESSAGES ===== */

.user-dashboard-product-success-container {
    background-color: #d1fae5 !important;
    border: 1px solid #a7f3d0 !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
    position: relative !important;
    z-index: 15 !important;
}

.user-dashboard-product-success-header {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    margin-bottom: 0.5rem !important;
}

.user-dashboard-product-success-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #059669 !important;
    flex-shrink: 0 !important;
}

.user-dashboard-product-success-title {
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #065f46 !important;
    margin: 0 !important;
}

.user-dashboard-product-success-message {
    font-size: 0.875rem !important;
    color: #065f46 !important;
    line-height: 1.6 !important;
    margin: 0 !important;
    white-space: pre-line !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* ===== INFO MESSAGES ===== */

.user-dashboard-product-info-container {
    background-color: #dbeafe !important;
    border: 1px solid #93c5fd !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
    position: relative !important;
    z-index: 15 !important;
}

.user-dashboard-product-info-header {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    margin-bottom: 0.5rem !important;
}

.user-dashboard-product-info-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #2563eb !important;
    flex-shrink: 0 !important;
}

.user-dashboard-product-info-title {
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #1e40af !important;
    margin: 0 !important;
}

.user-dashboard-product-info-message {
    font-size: 0.875rem !important;
    color: #1e40af !important;
    line-height: 1.6 !important;
    margin: 0 !important;
    white-space: pre-line !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Enhanced message styling */
.user-dashboard-product-error-item,
.user-dashboard-product-success-message,
.user-dashboard-product-info-message {
    white-space: pre-line !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
}

/* ===== EMPTY STATE ===== */

.user-dashboard-product-empty-state {
    text-align: center !important;
    padding: 3rem 1rem !important;
    background-color: #ffffff !important;
}

.user-dashboard-product-empty-icon {
    margin: 0 auto 1rem auto !important;
    width: 3rem !important;
    height: 3rem !important;
    color: #d1d5db !important;
}

.user-dashboard-product-empty-icon svg {
    width: 100% !important;
    height: 100% !important;
}

.user-dashboard-product-empty-title {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin: 0 0 0.5rem 0 !important;
}

.user-dashboard-product-empty-description {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    line-height: 1.5 !important;
    margin: 0 0 1.5rem 0 !important;
}

/* ===== PAGINATION WRAPPER ===== */

.user-dashboard-product-pagination-wrapper {
    padding: 1rem !important;
    border-top: 1px solid #e5e7eb !important;
    background-color: #f9fafb !important;
    position: relative !important;
    z-index: 10 !important;
}

/* ===== MOBILE RESPONSIVE ===== */

@media (max-width: 768px) {
    .user-dashboard-product-stats-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.75rem !important;
    }

    .user-dashboard-product-stat-card {
        padding: 0.75rem !important;
    }

    .user-dashboard-product-stat-value {
        font-size: 1.25rem !important;
    }

    .user-dashboard-product-search-form {
        flex-direction: column !important;
        align-items: stretch !important;
    }

    .user-dashboard-product-input-group {
        min-width: auto !important;
    }

    .user-dashboard-product-table-container {
        overflow-x: scroll !important;
    }

    .user-dashboard-product-table {
        min-width: 600px !important;
    }

    .user-dashboard-product-actions {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 0.25rem !important;
    }

    .user-dashboard-product-btn {
        width: 100% !important;
        justify-content: center !important;
    }

    .user-dashboard-product-preview-row {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 0.25rem !important;
    }
}

@media (max-width: 480px) {
    .user-dashboard-product-stats-grid {
        grid-template-columns: 1fr !important;
    }

    .user-dashboard-product-stock-value {
        font-size: 1.5rem !important;
    }

    .user-dashboard-product-form-wrapper {
        padding: 1rem !important;
    }
}

/* ===== CUSTOM MODAL DIALOGS ===== */

.user-dashboard-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 9999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease-in-out !important;
}

.user-dashboard-modal-overlay.user-dashboard-modal-show {
    opacity: 1 !important;
    visibility: visible !important;
}

.user-dashboard-modal-container {
    width: 100% !important;
    max-width: 500px !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    transform: scale(0.9) translateY(-20px) !important;
    transition: transform 0.3s ease-in-out !important;
}

.user-dashboard-modal-overlay.user-dashboard-modal-show .user-dashboard-modal-container {
    transform: scale(1) translateY(0) !important;
}

.user-dashboard-modal-content {
    background-color: #ffffff !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    overflow: hidden !important;
    position: relative !important;
}

/* Modal Type Styling */
.user-dashboard-modal-info {
    border-top: 4px solid #3b82f6 !important;
}

.user-dashboard-modal-warning {
    border-top: 4px solid #f59e0b !important;
}

.user-dashboard-modal-danger {
    border-top: 4px solid #ef4444 !important;
}

/* ===== MODAL HEADER ===== */

.user-dashboard-modal-header {
    display: flex !important;
    align-items: flex-start !important;
    gap: 1rem !important;
    padding: 1.5rem 1.5rem 1rem 1.5rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.user-dashboard-modal-icon-container {
    flex-shrink: 0 !important;
    width: 3rem !important;
    height: 3rem !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.user-dashboard-modal-icon {
    width: 1.5rem !important;
    height: 1.5rem !important;
}

.user-dashboard-modal-icon-info {
    color: #3b82f6 !important;
}

.user-dashboard-modal-info .user-dashboard-modal-icon-container {
    background-color: #dbeafe !important;
}

.user-dashboard-modal-icon-warning {
    color: #f59e0b !important;
}

.user-dashboard-modal-warning .user-dashboard-modal-icon-container {
    background-color: #fef3c7 !important;
}

.user-dashboard-modal-icon-danger {
    color: #ef4444 !important;
}

.user-dashboard-modal-danger .user-dashboard-modal-icon-container {
    background-color: #fee2e2 !important;
}

.user-dashboard-modal-header-content {
    flex: 1 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
}

.user-dashboard-modal-title {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

.user-dashboard-modal-close {
    background: none !important;
    border: none !important;
    color: #6b7280 !important;
    cursor: pointer !important;
    padding: 0.25rem !important;
    border-radius: 0.25rem !important;
    transition: all 0.15s ease-in-out !important;
    width: 1.5rem !important;
    height: 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.user-dashboard-modal-close:hover {
    color: #374151 !important;
    background-color: #f3f4f6 !important;
}

.user-dashboard-modal-close svg {
    width: 1rem !important;
    height: 1rem !important;
}

/* ===== MODAL BODY ===== */

.user-dashboard-modal-body {
    padding: 1rem 1.5rem !important;
}

.user-dashboard-modal-message {
    font-size: 0.875rem !important;
    color: #374151 !important;
    line-height: 1.6 !important;
    margin-bottom: 1rem !important;
    white-space: pre-line !important;
}

/* ===== PRODUCT INFO SECTION ===== */

.user-dashboard-modal-info-section {
    background-color: #f9fafb !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
}

.user-dashboard-modal-info-title {
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin: 0 0 0.75rem 0 !important;
}

.user-dashboard-modal-info-grid {
    display: grid !important;
    gap: 0.5rem !important;
}

.user-dashboard-modal-info-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.user-dashboard-modal-info-label {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
}

.user-dashboard-modal-info-value {
    font-size: 0.875rem !important;
    color: #111827 !important;
    font-weight: 600 !important;
}

/* ===== IMPACT PREVIEW SECTION ===== */

.user-dashboard-modal-impact-preview {
    margin-top: 1rem !important;
}

.user-dashboard-modal-impact-grid {
    display: grid !important;
    gap: 0.5rem !important;
}

.user-dashboard-modal-impact-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.5rem 0 !important;
}

.user-dashboard-modal-impact-result {
    border-top: 1px solid #d1d5db !important;
    margin-top: 0.5rem !important;
    padding-top: 1rem !important;
    font-weight: 600 !important;
}

.user-dashboard-modal-impact-label {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
}

.user-dashboard-modal-impact-value {
    font-size: 0.875rem !important;
    color: #111827 !important;
    font-weight: 600 !important;
}

.user-dashboard-modal-impact-change {
    color: #dc2626 !important;
}

/* ===== MODAL FOOTER ===== */

.user-dashboard-modal-footer {
    display: flex !important;
    gap: 0.75rem !important;
    justify-content: flex-end !important;
    padding: 1rem 1.5rem 1.5rem 1.5rem !important;
    background-color: #f9fafb !important;
    border-top: 1px solid #f3f4f6 !important;
}

.user-dashboard-modal-btn {
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem 1.5rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border-radius: 0.5rem !important;
    cursor: pointer !important;
    outline: none !important;
    transition: all 0.15s ease-in-out !important;
    touch-action: manipulation !important;
    box-sizing: border-box !important;
    text-decoration: none !important;
    user-select: none !important;
    border: 1px solid transparent !important;
    min-height: 44px !important;
    min-width: 100px !important;
    justify-content: center !important;
}

.user-dashboard-modal-btn-icon {
    width: 1rem !important;
    height: 1rem !important;
    flex-shrink: 0 !important;
}

.user-dashboard-modal-btn-secondary {
    color: #374151 !important;
    background-color: #ffffff !important;
    border-color: #d1d5db !important;
}

.user-dashboard-modal-btn-secondary:hover {
    background-color: #f9fafb !important;
    border-color: #9ca3af !important;
}

.user-dashboard-modal-btn-primary {
    color: #ffffff !important;
    background-color: #166534 !important;
    border-color: #166534 !important;
}

.user-dashboard-modal-btn-primary:hover {
    background-color: #15803d !important;
    border-color: #15803d !important;
}

/* Danger variant for destructive actions */
.user-dashboard-modal-btn-danger {
    color: #ffffff !important;
    background-color: #dc2626 !important;
    border-color: #dc2626 !important;
}

.user-dashboard-modal-btn-danger:hover {
    background-color: #b91c1c !important;
    border-color: #b91c1c !important;
}

/* ===== MODAL MOBILE RESPONSIVE ===== */

@media (max-width: 640px) {
    .user-dashboard-modal-overlay {
        padding: 0.5rem !important;
    }

    .user-dashboard-modal-container {
        max-width: 100% !important;
    }

    .user-dashboard-modal-header {
        padding: 1rem 1rem 0.75rem 1rem !important;
        flex-direction: column !important;
        align-items: center !important;
        text-align: center !important;
        gap: 0.75rem !important;
    }

    .user-dashboard-modal-header-content {
        width: 100% !important;
        flex-direction: column !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    .user-dashboard-modal-close {
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
    }

    .user-dashboard-modal-body {
        padding: 0.75rem 1rem !important;
    }

    .user-dashboard-modal-footer {
        padding: 1rem !important;
        flex-direction: column !important;
        gap: 0.5rem !important;
    }

    .user-dashboard-modal-btn {
        width: 100% !important;
        order: 2 !important;
    }

    .user-dashboard-modal-btn-secondary {
        order: 1 !important;
    }

    .user-dashboard-modal-info-item,
    .user-dashboard-modal-impact-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 0.25rem !important;
    }

    .user-dashboard-modal-info-label,
    .user-dashboard-modal-impact-label {
        font-size: 0.75rem !important;
    }
}

/* ===== MODAL ACCESSIBILITY ===== */

.user-dashboard-modal-overlay[aria-hidden="true"] {
    display: none !important;
}

.user-dashboard-modal-content:focus {
    outline: 2px solid #166534 !important;
    outline-offset: 2px !important;
}

/* ===== MODAL ANIMATIONS ===== */

@keyframes user-dashboard-modal-fade-in {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes user-dashboard-modal-fade-out {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
}

.user-dashboard-modal-overlay.user-dashboard-modal-entering .user-dashboard-modal-container {
    animation: user-dashboard-modal-fade-in 0.3s ease-out forwards !important;
}

.user-dashboard-modal-overlay.user-dashboard-modal-leaving .user-dashboard-modal-container {
    animation: user-dashboard-modal-fade-out 0.3s ease-in forwards !important;
}
